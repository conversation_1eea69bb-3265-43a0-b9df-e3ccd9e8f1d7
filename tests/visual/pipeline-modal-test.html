<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pipeline Modal Styling Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for testing */
        .pipeline-modal {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .console-log {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        /* Simulate the extension popup constraints */
        .extension-container {
            width: 400px;
            height: 600px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        
        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Pipeline Modal Styling Test</h1>
        
        <!-- Test Container -->
        <div class="extension-container mx-auto bg-white">
            <div class="modal-overlay">
                <!-- Pipeline Modal -->
                <div class="bg-white rounded-lg shadow-2xl flex flex-col w-full h-full max-h-screen pipeline-modal">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                        <div class="flex items-center space-x-3">
                            <h2 class="text-lg font-semibold text-gray-900">Multi-Step Processing Pipeline</h2>
                            <span class="text-sm text-gray-600 truncate max-w-xs">Processing: Pipeline-327_K_06_23_PCM.pdf</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">
                                <span>▶</span>
                                <span>Start</span>
                            </button>
                            <button class="p-2 text-gray-500 hover:text-gray-700">
                                <span>✕</span>
                            </button>
                        </div>
                    </div>

                    <!-- Overall Progress -->
                    <div class="px-4 py-2 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">Overall Progress</span>
                            <span class="text-sm text-gray-600">25%</span>
                        </div>
                        <div class="w-full bg-gray-300 rounded-full h-1.5">
                            <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300" style="width: 25%"></div>
                        </div>
                    </div>

                    <!-- Main Content - Split Layout -->
                    <div class="flex-1 flex overflow-hidden min-h-0">
                        <!-- Left Side - Pipeline Steps -->
                        <div class="w-1/2 border-r border-gray-200 overflow-y-auto flex-shrink-0">
                            <div class="p-4">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Pipeline Steps</h3>
                                <div class="space-y-4">
                                    <!-- Step 1 -->
                                    <div class="relative p-3 rounded-lg border-2 bg-green-50 border-green-300">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-lg text-green-600">📄</span>
                                            <div class="flex-1">
                                                <h4 class="font-medium text-green-800">PDF Text Extraction</h4>
                                                <p class="text-xs text-green-600">Extract text content from PDF using PDF.js</p>
                                            </div>
                                            <span class="text-sm text-green-600">✓</span>
                                        </div>
                                    </div>

                                    <!-- Step 2 -->
                                    <div class="relative p-3 rounded-lg border-2 bg-blue-50 border-blue-300">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-lg text-blue-600">🤖</span>
                                            <div class="flex-1">
                                                <h4 class="font-medium text-blue-800">DeepSeek AI Analysis - Structure</h4>
                                                <p class="text-xs text-blue-600">Analyze document kind, language, basic info</p>
                                            </div>
                                            <div class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                                        </div>
                                    </div>

                                    <!-- Step 3 -->
                                    <div class="relative p-3 rounded-lg border-2 bg-gray-50 border-gray-300">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-lg text-gray-400">🔗</span>
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-600">RAG Document Linking</h4>
                                                <p class="text-xs text-gray-500">Link similar documents and enhance prompts using RAG</p>
                                            </div>
                                            <span class="text-sm text-gray-400">⏳</span>
                                        </div>
                                    </div>

                                    <!-- Step 4 -->
                                    <div class="relative p-3 rounded-lg border-2 bg-gray-50 border-gray-300">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-lg text-gray-400">👁️</span>
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-600">OCR Structural Reference</h4>
                                                <p class="text-xs text-gray-500">Extract OCR text for structural reference using Tesseract</p>
                                            </div>
                                            <span class="text-sm text-gray-400">⏳</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side - Live Console -->
                        <div class="w-1/2 flex flex-col min-h-0 flex-shrink-0">
                            <div class="px-4 py-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
                                <h3 class="text-lg font-medium text-gray-900">Live Console</h3>
                            </div>
                            <div class="flex-1 min-h-0 overflow-hidden">
                                <!-- Console Logger -->
                                <div class="flex flex-col h-full bg-gray-900 text-green-400 font-mono text-xs console-log">
                                    <!-- Console Header -->
                                    <div class="flex items-center justify-between p-2 bg-gray-800 border-b border-gray-700">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-green-400">⚡</span>
                                            <h4 class="text-white font-semibold">Console</h4>
                                            <div class="flex items-center space-x-1">
                                                <div class="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                                                <span class="text-xs text-gray-400">Live</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-1">
                                            <button class="px-2 py-1 text-xs rounded bg-green-600 text-white">Auto-scroll</button>
                                        </div>
                                    </div>

                                    <!-- Filters -->
                                    <div class="flex items-center space-x-1 px-2 py-1 bg-gray-800 border-b border-gray-700 text-xs">
                                        <div class="flex-1 relative min-w-0">
                                            <input type="text" placeholder="Search..." class="w-full pl-6 pr-2 py-0.5 bg-gray-700 text-white text-xs rounded border border-gray-600">
                                        </div>
                                        <select class="px-1.5 py-0.5 bg-gray-700 text-white text-xs rounded border border-gray-600">
                                            <option>All</option>
                                            <option>Error</option>
                                            <option>Info</option>
                                        </select>
                                    </div>

                                    <!-- Console Content -->
                                    <div class="flex-1 overflow-y-auto px-2 py-1 space-y-1">
                                        <div class="text-center text-gray-500 py-8">
                                            <div class="flex items-start space-x-2 hover:bg-gray-800 px-1 py-0.5 rounded">
                                                <span class="text-gray-500 text-xs flex-shrink-0 w-16">15:21:08</span>
                                                <div class="flex-shrink-0 text-blue-400">ℹ️</div>
                                                <span class="bg-blue-600 text-white px-1 py-0.5 text-xs rounded flex-shrink-0">PDF</span>
                                                <span class="break-words">Starting PDF text extraction...</span>
                                            </div>
                                            <div class="flex items-start space-x-2 hover:bg-gray-800 px-1 py-0.5 rounded">
                                                <span class="text-gray-500 text-xs flex-shrink-0 w-16">15:21:09</span>
                                                <div class="flex-shrink-0 text-green-400">✅</div>
                                                <span class="bg-green-600 text-white px-1 py-0.5 text-xs rounded flex-shrink-0">PDF</span>
                                                <span class="break-words">PDF extraction completed successfully</span>
                                            </div>
                                            <div class="flex items-start space-x-2 hover:bg-gray-800 px-1 py-0.5 rounded">
                                                <span class="text-gray-500 text-xs flex-shrink-0 w-16">15:21:10</span>
                                                <div class="flex-shrink-0 text-blue-400">ℹ️</div>
                                                <span class="bg-purple-600 text-white px-1 py-0.5 text-xs rounded flex-shrink-0">AI</span>
                                                <span class="break-words">Starting DeepSeek analysis - document structure...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Section -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Styling Analysis</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">✅ Improvements Made:</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Changed back to 50/50 split for better balance</li>
                        <li>• Added compact mode for pipeline steps</li>
                        <li>• Reduced padding and margins throughout</li>
                        <li>• Optimized console header and filters</li>
                        <li>• Added flex-shrink-0 to prevent layout collapse</li>
                        <li>• Improved height management with min-h-0</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">🎯 Key Features:</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Console stretches to full available height</li>
                        <li>• Balanced layout proportions</li>
                        <li>• Compact, efficient use of space</li>
                        <li>• Clean visual hierarchy</li>
                        <li>• Responsive design within constraints</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
