# Manual Test: Pipeline Window Styling and Refresh Fix

## Test Overview
This manual test verifies the improvements made to pipeline window styling and refresh functionality.

## Prerequisites
- Chrome extension built with `make dev-extension`
- Extension loaded in Chrome
- Sample PDF files available in `data/samples/invoices/input/`

## Test Steps

### 1. Open Extension
1. Click the MVAT extension icon in Chrome
2. Navigate to the **Upload** tab
3. Verify the upload interface loads correctly

### 2. Upload a Test File
1. Drag and drop a PDF file from `data/samples/invoices/input/` (e.g., `10173_M_07_24.pdf`)
2. Wait for the file to be processed
3. Verify the file appears in the "Recent Uploads" section

### 3. Open Pipeline Window
1. Find the uploaded file in the Recent Uploads section
2. Click the **Pipeline** button for that file
3. Verify a new pipeline window opens

### 4. Test Improved Styling
**Expected Results:**
- Pipeline step cards have consistent styling with proper shadows and borders
- Action buttons (Rerun, Show Input, Show Output) use extension-styled design:
  - Proper padding and border radius
  - Consistent hover effects
  - Primary buttons are blue with white text
  - Secondary buttons are gray with white text
  - Outline buttons have borders and gray text
- Step cards have proper visual hierarchy
- Console logs section integrates well with the overall design

**Visual Checks:**
- [ ] Step cards have rounded corners (12px border radius)
- [ ] Cards have subtle shadows
- [ ] Active step cards have blue border and background
- [ ] Completed steps have green indicators
- [ ] Error steps have red indicators
- [ ] Buttons have consistent styling across all steps
- [ ] Hover effects work smoothly
- [ ] Typography is consistent (Inter font family)

### 5. Test Refresh Functionality
1. With the pipeline window open, press **F5** or **Ctrl+R** to refresh
2. **Expected Results:**
   - Pipeline window content should be restored
   - Window title should show "Restored" indicator
   - All step cards and their states should be preserved
   - Console logs should be maintained
   - No "about:blank" page should appear

**Verification Steps:**
- [ ] Pipeline content appears after refresh (not blank page)
- [ ] Window title includes "Restored" indicator
- [ ] Step cards maintain their previous state
- [ ] File information is preserved
- [ ] Console logs are restored
- [ ] All buttons remain functional

### 6. Test Multiple Refreshes
1. Refresh the pipeline window 2-3 times
2. Verify content is restored each time
3. Check that no memory leaks or performance issues occur

### 7. Test Window Closing and Reopening
1. Close the pipeline window
2. Open it again from the main extension
3. Verify it opens with fresh content (not restored state)

## Expected Improvements

### Styling Improvements
- **Before**: Basic button styling, inconsistent card design
- **After**: Extension-consistent styling, proper visual hierarchy

### Refresh Functionality
- **Before**: Pipeline window shows "about:blank" after refresh
- **After**: Content is restored with proper state persistence

## Test Results Template

### Styling Test Results
- [ ] **PASS** - Step cards have improved styling
- [ ] **PASS** - Buttons use extension design system
- [ ] **PASS** - Visual hierarchy is clear
- [ ] **PASS** - Hover effects work properly
- [ ] **PASS** - Typography is consistent

### Refresh Test Results
- [ ] **PASS** - Content restored after refresh
- [ ] **PASS** - Restoration indicator shown
- [ ] **PASS** - State persistence works
- [ ] **PASS** - No blank page after refresh
- [ ] **PASS** - Multiple refreshes work

### Overall Assessment
- [ ] **PASS** - All styling improvements implemented
- [ ] **PASS** - Refresh functionality works reliably
- [ ] **PASS** - No console errors
- [ ] **PASS** - Performance is acceptable

## Screenshots
Take screenshots of:
1. Pipeline window before improvements (if available)
2. Pipeline window with new styling
3. Pipeline window after refresh showing restoration
4. Console logs showing no errors

## Notes
- Test with different file types if available
- Test with different window sizes
- Note any performance differences
- Document any remaining issues

## Completion Criteria
✅ All styling improvements are visible and functional
✅ Refresh functionality works without data loss
✅ No console errors or warnings
✅ User experience is significantly improved
