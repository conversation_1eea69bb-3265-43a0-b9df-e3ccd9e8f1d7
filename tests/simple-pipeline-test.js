#!/usr/bin/env node

/**
 * Simple Pipeline Modal Test
 * Quick test to verify pipeline modal styling improvements
 */

import { Builder, By, until } from 'selenium-webdriver';
import chrome from 'selenium-webdriver/chrome.js';
import fs from 'fs';
import path from 'path';

const EXTENSION_PATH = path.resolve('./dist/dev');
const SCREENSHOT_DIR = path.resolve('./tests/screenshots');

// Ensure screenshot directory exists
if (!fs.existsSync(SCREENSHOT_DIR)) {
  fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
}

console.log('🧪 SIMPLE PIPELINE MODAL TEST');
console.log('============================================================');
console.log('📅 Timestamp:', new Date().toISOString());
console.log('🎯 Purpose: Quick verification of pipeline modal styling');
console.log('============================================================\n');

/**
 * Setup Chrome driver with extension
 */
async function setupDriver() {
  console.log('🔧 Setting up Chrome driver...');
  
  const options = new chrome.Options();
  options.addArguments(`--load-extension=${EXTENSION_PATH}`);
  options.addArguments('--no-sandbox');
  options.addArguments('--disable-dev-shm-usage');
  options.addArguments('--disable-web-security');
  options.addArguments('--window-size=1200,800');
  
  const driver = await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .build();

  console.log('✅ Chrome driver setup complete');
  return driver;
}

/**
 * Test extension loading and basic functionality
 */
async function testExtensionBasics(driver) {
  console.log('🔧 Testing extension basics...');
  
  try {
    // Navigate to a simple page first
    await driver.get('data:text/html,<html><body><h1>Extension Test Page</h1></body></html>');
    await driver.sleep(2000);
    
    // Take screenshot of basic state
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, 'simple-01-basic-page.png'), data, 'base64');
    });
    
    console.log('✅ Basic page loaded successfully');
    
    // Try to access extension popup directly
    const extensionUrl = `chrome-extension://${await getExtensionId(driver)}/popup.html`;
    await driver.get(extensionUrl);
    await driver.sleep(3000);
    
    // Take screenshot of extension popup
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, 'simple-02-extension-popup.png'), data, 'base64');
    });
    
    console.log('✅ Extension popup accessed successfully');
    return true;
    
  } catch (error) {
    console.error('❌ Extension basics test failed:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, 'simple-02-extension-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Get extension ID (simplified approach)
 */
async function getExtensionId(driver) {
  try {
    // Try to get extension ID from chrome://extensions
    await driver.get('chrome://extensions/');
    await driver.sleep(2000);
    
    // Look for extension cards
    const scripts = await driver.executeScript(`
      const items = document.querySelectorAll('extensions-item');
      for (const item of items) {
        const name = item.shadowRoot?.querySelector('#name')?.textContent || '';
        if (name.includes('MVAT') || name.includes('Invoice') || name.includes('Accounting')) {
          const id = item.getAttribute('id');
          return id;
        }
      }
      return null;
    `);
    
    if (scripts) {
      console.log('✅ Extension ID found:', scripts);
      return scripts;
    }
    
    // Fallback: try common extension ID patterns
    const commonIds = [
      'abcdefghijklmnopqrstuvwxyzabcdef',
      'bcdefghijklmnopqrstuvwxyzabcdefa',
      'cdefghijklmnopqrstuvwxyzabcdefab'
    ];
    
    for (const id of commonIds) {
      try {
        await driver.get(`chrome-extension://${id}/popup.html`);
        await driver.sleep(1000);
        const title = await driver.getTitle();
        if (title && !title.includes('not found')) {
          console.log('✅ Extension ID found via fallback:', id);
          return id;
        }
      } catch (e) {
        continue;
      }
    }
    
    throw new Error('Could not determine extension ID');
    
  } catch (error) {
    console.log('⚠️ Using fallback extension ID');
    return 'abcdefghijklmnopqrstuvwxyzabcdef'; // Fallback
  }
}

/**
 * Test pipeline modal if possible
 */
async function testPipelineModal(driver, extensionId) {
  console.log('🔧 Testing pipeline modal...');
  
  try {
    // Navigate to extension popup
    await driver.get(`chrome-extension://${extensionId}/popup.html`);
    await driver.sleep(3000);
    
    // Look for any pipeline-related elements
    const pipelineElements = await driver.findElements(By.css('*[class*="pipeline"], *[data-testid*="pipeline"], button:contains("Pipeline")'));
    
    console.log(`📋 Found ${pipelineElements.length} pipeline-related elements`);
    
    // Take screenshot of current state
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, 'simple-03-pipeline-search.png'), data, 'base64');
    });
    
    // Try to click any pipeline button
    if (pipelineElements.length > 0) {
      try {
        await pipelineElements[0].click();
        await driver.sleep(2000);
        
        // Take screenshot after clicking
        await driver.takeScreenshot().then(data => {
          fs.writeFileSync(path.join(SCREENSHOT_DIR, 'simple-04-pipeline-clicked.png'), data, 'base64');
        });
        
        console.log('✅ Pipeline element clicked successfully');
      } catch (clickError) {
        console.log('⚠️ Could not click pipeline element:', clickError.message);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Pipeline modal test failed:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, 'simple-04-pipeline-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Main test runner
 */
async function runSimpleTest() {
  let driver;
  
  try {
    driver = await setupDriver();
    
    const tests = [
      () => testExtensionBasics(driver),
      () => testPipelineModal(driver, 'test-id')
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
      await driver.sleep(1000);
    }
    
    console.log('\n============================================================');
    console.log('📊 SIMPLE PIPELINE TEST RESULTS');
    console.log('============================================================');
    console.log(`✅ Tests Passed: ${passed}`);
    console.log(`❌ Tests Failed: ${failed}`);
    console.log(`📸 Screenshots saved to: ${SCREENSHOT_DIR}`);
    console.log('============================================================');
    
    if (failed === 0) {
      console.log('🎉 ALL SIMPLE TESTS PASSED!');
    } else {
      console.log('⚠️ SOME TESTS HAD ISSUES - CHECK SCREENSHOTS');
    }
    
  } catch (error) {
    console.error('💥 Simple test failed:', error);
  } finally {
    if (driver) {
      await driver.quit();
    }
  }
}

// Run tests
runSimpleTest().catch(error => {
  console.error('💥 Simple test runner failed:', error);
  process.exit(1);
});
