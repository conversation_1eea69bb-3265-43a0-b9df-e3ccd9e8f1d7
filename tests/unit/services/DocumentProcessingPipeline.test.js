/**
 * Unit tests for DocumentProcessingPipeline individual step methods
 * Tests the new individual step execution functionality for debug interface
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { DocumentProcessingPipeline } from '../../../src/services/DocumentProcessingPipeline.js';

// Mock dependencies
vi.mock('../../../src/utils/ProcessingLogger.js', () => ({
  processingLogger: {
    generateUploadId: vi.fn(() => 'test-upload-id-123'),
    logProcessingStart: vi.fn(),
    logProcessingComplete: vi.fn(),
    logProcessingError: vi.fn()
  }
}));

vi.mock('../../../src/services/PDFProcessingService.js', () => ({
  PDFProcessingService: vi.fn().mockImplementation(() => ({
    initialize: vi.fn().mockResolvedValue(true),
    extractText: vi.fn().mockResolvedValue({
      text: 'Sample PDF text content for testing',
      pages: 1,
      metadata: { title: 'Test Document' }
    })
  }))
}));

vi.mock('../../../src/services/SandboxCommunicationService.js', () => ({
  sandboxCommunicationService: {
    initialize: vi.fn().mockResolvedValue(true),
    initializeTesseract: vi.fn().mockResolvedValue({ success: true, language: 'pol+eng' }),
    processOCR: vi.fn().mockResolvedValue({
      text: 'OCR extracted text content',
      confidence: 85
    })
  }
}));

vi.mock('../../../src/services/EnhancedDeepSeekAnalysis.js', () => ({
  enhancedDeepSeekAnalysis: {
    performComprehensiveAnalysis: vi.fn().mockResolvedValue({
      confidenceScore: { overall: 90 },
      metadata: {
        companyRelationships: {
          vendor: 'Test Vendor',
          customer: 'Test Customer'
        },
        transactionPatterns: {
          amounts: { net: 100, tax: 23, gross: 123 },
          currency: 'PLN'
        },
        documentMetadata: {
          invoiceNumber: 'INV-001',
          invoiceDate: '2025-06-16'
        }
      }
    })
  }
}));

vi.mock('../../../src/services/PipelineDataStorageService.js', () => ({
  pipelineDataStorage: {
    storeDocumentData: vi.fn().mockResolvedValue(true),
    storeStepResult: vi.fn().mockResolvedValue(true)
  }
}));

describe('DocumentProcessingPipeline Individual Steps', () => {
  let pipeline;
  let mockFile;

  beforeEach(() => {
    pipeline = new DocumentProcessingPipeline();
    mockFile = new File(['test content'], 'test-invoice.pdf', { type: 'application/pdf' });

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('runPdfExtraction', () => {
    it('should successfully extract PDF text', async () => {
      const result = await pipeline.runPdfExtraction(mockFile);

      expect(result.success).toBe(true);
      expect(result.stepId).toBe('pdf_extraction');
      expect(result.data.text).toBe('Sample PDF text content for testing');
      expect(result.data.pages).toBe(1);
      expect(result.timing).toBeGreaterThanOrEqual(0);
    });

    it('should handle PDF extraction errors', async () => {
      // Mock PDF service to throw error
      pipeline.pdfService.extractText.mockRejectedValue(new Error('PDF extraction failed'));

      const result = await pipeline.runPdfExtraction(mockFile);

      expect(result.success).toBe(false);
      expect(result.stepId).toBe('pdf_extraction');
      expect(result.error).toBe('PDF extraction failed');
    });
  });

  describe('runDeepSeekAnalysis', () => {
    it('should successfully analyze text with DeepSeek', async () => {
      const pdfText = 'Sample invoice text with sufficient content for analysis';
      const options = { apiKey: 'test-api-key' };

      const result = await pipeline.runDeepSeekAnalysis(pdfText, options);

      expect(result.success).toBe(true);
      expect(result.stepId).toBe('deepseek_analysis');
      expect(result.data.confidence).toBe(90);
      expect(result.timing).toBeGreaterThanOrEqual(0);
    });

    it('should fail without API key', async () => {
      const pdfText = 'Sample text';
      const options = {}; // No API key

      const result = await pipeline.runDeepSeekAnalysis(pdfText, options);

      expect(result.success).toBe(false);
      expect(result.error).toBe('DeepSeek API key required for analysis');
    });

    it('should fail with insufficient text', async () => {
      const pdfText = 'Short'; // Too short
      const options = { apiKey: 'test-api-key' };

      const result = await pipeline.runDeepSeekAnalysis(pdfText, options);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Insufficient text for DeepSeek analysis');
    });
  });

  describe('runTesseractReference', () => {
    it('should successfully process OCR', async () => {
      // Mock convertPdfToImage to return image data
      pipeline.convertPdfToImage = vi.fn().mockResolvedValue('mock-image-data');

      const result = await pipeline.runTesseractReference(mockFile);

      expect(result.success).toBe(true);
      expect(result.stepId).toBe('tesseract_reference');
      expect(result.data.text).toBe('OCR extracted text content');
      expect(result.data.confidence).toBe(85);
    });

    it('should handle PDF to image conversion failure', async () => {
      // Mock convertPdfToImage to return null
      pipeline.convertPdfToImage = vi.fn().mockResolvedValue(null);

      const result = await pipeline.runTesseractReference(mockFile);

      expect(result.success).toBe(true); // Should still succeed but with no OCR data
      expect(result.data.success).toBe(false);
      expect(result.data.reason).toBe('PDF to image conversion failed');
    });
  });

  describe('runFieldMapping', () => {
    it('should successfully map fields', async () => {
      const pdfResult = { text: 'Sample PDF text' };
      const deepSeekResult = {
        success: true,
        extractedFields: { invoice_number: 'INV-001' }
      };
      const ocrResult = { success: true, text: 'OCR text' };

      const result = await pipeline.runFieldMapping(pdfResult, deepSeekResult, ocrResult);

      expect(result.success).toBe(true);
      expect(result.stepId).toBe('field_mapping');
      expect(result.data.mappedFields).toBeDefined();
      expect(result.timing).toBeGreaterThanOrEqual(0);
    });
  });

  describe('runDataValidation', () => {
    it('should successfully validate data', async () => {
      const mappingResult = {
        success: true,
        mappedFields: { invoice_number: 'INV-001', total_gross: 123 },
        documentType: 'invoice'
      };

      const result = await pipeline.runDataValidation(mappingResult);

      expect(result.success).toBe(true);
      expect(result.stepId).toBe('data_validation');
      expect(result.data.validatedFields).toBeDefined();
      expect(result.timing).toBeGreaterThanOrEqual(0);
    });

    it('should handle mapping failure', async () => {
      const mappingResult = { success: false };

      const result = await pipeline.runDataValidation(mappingResult);

      expect(result.success).toBe(true); // Method succeeds but validation fails
      expect(result.data.success).toBe(false);
      expect(result.data.reason).toBe('Mapping step failed');
    });
  });

  describe('runFinalOutput', () => {
    it('should successfully generate final output', async () => {
      const validatedData = {
        success: true,
        validatedFields: { invoice_number: 'INV-001' },
        accuracyScore: 90
      };

      const result = await pipeline.runFinalOutput(mockFile, validatedData);

      expect(result.success).toBe(true);
      expect(result.stepId).toBe('final_output');
      expect(result.data).toBeDefined();
      expect(result.timing).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Integration Tests', () => {
    it('should store step results for debugging', async () => {
      const result = await pipeline.runPdfExtraction(mockFile);

      expect(result.success).toBe(true);
      expect(pipeline.stepResults.has('pdf_extraction')).toBe(true);
      expect(pipeline.stepTimings.has('pdf_extraction')).toBe(true);
    });

    it('should handle initialization properly', async () => {
      // Test that initialize is called
      await pipeline.runPdfExtraction(mockFile);

      expect(pipeline.pdfService.initialize).toHaveBeenCalled();
      expect(pipeline.sandboxService.initialize).toHaveBeenCalled();
    });
  });
});

// 🧪 Self-test execution
if (import.meta.vitest) {
  console.log('🧪 DocumentProcessingPipeline unit tests loaded');
}
