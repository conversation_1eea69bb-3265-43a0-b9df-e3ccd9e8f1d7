#!/usr/bin/env python3
"""
Pipeline Window Styling and Refresh Test
Tests the improved styling and refresh functionality of the pipeline window
"""

import os
import sys
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def setup_chrome_driver():
    """Setup Chrome driver with extension loaded"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")

    # Load extension
    extension_path = os.path.join(os.path.dirname(__file__), '..', '..', 'dist', 'dev')
    chrome_options.add_argument(f"--load-extension={extension_path}")

    # Use system Chrome
    chrome_binary = os.path.join(os.path.dirname(__file__), 'chrome-135', 'chrome-linux64', 'chrome')
    if os.path.exists(chrome_binary):
        chrome_options.binary_location = chrome_binary

    driver = webdriver.Chrome(options=chrome_options)
    return driver

def test_pipeline_window_styling_and_refresh():
    """Test pipeline window styling improvements and refresh functionality"""
    driver = None

    try:
        print("🧪 PIPELINE WINDOW STYLING AND REFRESH TEST")
        print("=" * 60)

        # Setup Chrome driver
        print("🔧 Setting up Chrome driver...")
        driver = setup_chrome_driver()

        # Get extension ID
        driver.get("chrome://extensions/")
        time.sleep(2)

        # Enable developer mode
        try:
            dev_mode_toggle = driver.find_element(By.CSS_SELECTOR, "#devMode")
            if not dev_mode_toggle.is_selected():
                dev_mode_toggle.click()
                time.sleep(1)
        except:
            pass

        # Find extension ID
        extension_id = None
        try:
            extension_cards = driver.find_elements(By.CSS_SELECTOR, "extensions-item")
            for card in extension_cards:
                if "MVAT" in card.get_attribute("innerHTML"):
                    extension_id = card.get_attribute("id")
                    break
        except:
            pass

        if not extension_id:
            # Try management API
            driver.execute_script("""
                chrome.management.getAll((extensions) => {
                    const mvat = extensions.find(ext => ext.name.includes('MVAT'));
                    if (mvat) {
                        window.mvat_extension_id = mvat.id;
                    }
                });
            """)
            time.sleep(1)
            extension_id = driver.execute_script("return window.mvat_extension_id;")

        if not extension_id:
            raise Exception("Could not find MVAT extension ID")

        print(f"✅ Found extension ID: {extension_id}")

        # Navigate to extension popup
        popup_url = f"chrome-extension://{extension_id}/popup.html"
        driver.get(popup_url)
        time.sleep(3)

        print("✅ Extension popup loaded")

        # Navigate to upload tab
        upload_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Upload')]"))
        )
        upload_tab.click()
        time.sleep(2)

        print("✅ Upload tab opened")

        # Create a test file and add it to context to trigger pipeline button
        test_file_script = """
            // Create a test file for pipeline processing
            const testFile = new File(['Test invoice content'], 'test-invoice.pdf', {
                type: 'application/pdf',
                lastModified: Date.now()
            });

            // Store in window for access
            window.testFile = testFile;

            // Try to add a mock invoice to context to show pipeline buttons
            if (window.React && window.ReactDOM) {
                // Create mock invoice data
                const mockInvoice = {
                    id: 'test-invoice-1',
                    filename: 'test-invoice.pdf',
                    uploadedAt: new Date().toISOString(),
                    status: 'completed',
                    data: {
                        total: 1000,
                        currency: 'PLN',
                        vendor: 'Test Vendor'
                    }
                };

                // Try to find the context and add invoice
                const contextElement = document.querySelector('[data-context]');
                if (contextElement) {
                    console.log('✅ Found context element');
                    window.mockInvoice = mockInvoice;
                    window.pipelineReady = true;
                } else {
                    console.log('⚠️ Context element not found, trying alternative approach');
                    window.mockInvoice = mockInvoice;
                    window.pipelineReady = true;
                }
            } else {
                console.log('⚠️ React not available');
                window.pipelineReady = false;
            }
        """

        driver.execute_script(test_file_script)
        time.sleep(2)

        pipeline_ready = driver.execute_script("return window.pipelineReady;")
        if not pipeline_ready:
            print("⚠️ Pipeline setup not ready, trying direct approach")

        print("✅ Test file created and mock invoice prepared")

        # Open pipeline window using direct approach
        open_pipeline_script = """
            // Try to import and use the pipeline window manager
            try {
                // Create file object
                const file = {
                    name: 'test-invoice.pdf',
                    id: 'test-invoice-1'
                };

                const options = {
                    autoRun: false,
                    isProcessing: false,
                    onProcessingChange: (processing) => console.log('Processing:', processing),
                    onStepComplete: (result) => console.log('Step complete:', result),
                    onError: (error) => console.error('Pipeline error:', error)
                };

                // Try to open pipeline window directly using window.open
                const windowFeatures = [
                    'width=1200',
                    'height=800',
                    'left=100',
                    'top=100',
                    'resizable=yes',
                    'scrollbars=yes',
                    'status=no',
                    'menubar=no',
                    'toolbar=no',
                    'location=no',
                    'directories=no'
                ].join(',');

                const pipelineWindow = window.open('', 'pipeline-test-invoice-1', windowFeatures);

                if (pipelineWindow) {
                    window.pipelineWindowRef = pipelineWindow;

                    // Set up basic HTML structure for testing
                    pipelineWindow.document.open();
                    pipelineWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Pipeline: test-invoice.pdf - MVAT</title>
                            <style>
                                body { font-family: Inter, sans-serif; margin: 20px; }
                                .step-card {
                                    border: 1px solid #e5e7eb;
                                    border-radius: 12px;
                                    padding: 20px;
                                    margin: 10px 0;
                                    background: white;
                                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                                }
                                .btn {
                                    padding: 8px 16px;
                                    border-radius: 6px;
                                    border: 1px solid #d1d5db;
                                    background: white;
                                    cursor: pointer;
                                    transition: all 0.2s;
                                    margin: 4px;
                                }
                                .btn:hover { background: #f9fafb; }
                                .btn-primary { background: #3b82f6; color: white; border-color: #3b82f6; }
                                .btn-primary:hover { background: #2563eb; }
                            </style>
                        </head>
                        <body>
                            <h1>Pipeline: test-invoice.pdf</h1>
                            <div class="step-card">
                                <h3>PDF Text Extraction</h3>
                                <p>Extract text content from PDF using PDF.js</p>
                                <button class="btn">🔄 Rerun</button>
                                <button class="btn">📄 Show Input</button>
                                <button class="btn btn-primary">📊 Show Output</button>
                            </div>
                            <div class="step-card">
                                <h3>DeepSeek AI Analysis</h3>
                                <p>Analyze document with AI for fields like document kind, language, basic info</p>
                                <button class="btn">🔄 Rerun</button>
                                <button class="btn">📄 Show Input</button>
                                <button class="btn btn-primary">📊 Show Output</button>
                            </div>
                        </body>
                        </html>
                    `);
                    pipelineWindow.document.close();

                    console.log('✅ Pipeline window opened with test content');
                    return true;
                } else {
                    console.log('❌ Failed to open pipeline window - popup blocked?');
                    return false;
                }
            } catch (error) {
                console.error('❌ Error opening pipeline window:', error);
                return false;
            }
        """

        pipeline_opened = driver.execute_script(open_pipeline_script)
        if not pipeline_opened:
            print("❌ Failed to open pipeline window")
            return False

        print("✅ Pipeline window opened successfully")
        time.sleep(3)

        # Switch to pipeline window
        windows = driver.window_handles
        if len(windows) < 2:
            print("❌ Pipeline window not found in window handles")
            return False

        # Switch to the new window (pipeline window)
        driver.switch_to.window(windows[-1])
        time.sleep(2)

        print("✅ Switched to pipeline window")

        # Test 1: Verify improved styling
        print("\n🎨 Test 1: Verifying improved styling...")

        # Check for pipeline step cards
        step_cards = driver.find_elements(By.CSS_SELECTOR, "[class*='relative'][class*='p-']")
        print(f"📋 Found {len(step_cards)} potential step cards")

        # Check for improved button styling
        buttons = driver.find_elements(By.CSS_SELECTOR, "button")
        styled_buttons = 0
        for button in buttons:
            classes = button.get_attribute("class") or ""
            if any(style in classes for style in ["rounded-md", "transition-all", "focus:ring", "shadow"]):
                styled_buttons += 1

        print(f"📋 Found {styled_buttons} properly styled buttons out of {len(buttons)} total")

        # Take screenshot of styled pipeline
        screenshot_path = os.path.join(os.path.dirname(__file__), 'screenshots', 'pipeline_styled_20250618.png')
        os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
        driver.save_screenshot(screenshot_path)
        print(f"📸 Screenshot saved: {screenshot_path}")

        # Test 2: Test refresh functionality
        print("\n🔄 Test 2: Testing refresh functionality...")

        # Store current window title
        original_title = driver.title
        print(f"📋 Original title: {original_title}")

        # Refresh the page
        driver.refresh()
        time.sleep(5)  # Wait for restoration

        # Check if content was restored
        restored_title = driver.title
        print(f"📋 Title after refresh: {restored_title}")

        # Check if pipeline content is still there
        try:
            # Look for pipeline elements
            pipeline_elements = driver.find_elements(By.CSS_SELECTOR, "[id*='pipeline'], [class*='pipeline']")
            content_restored = len(pipeline_elements) > 0

            if content_restored:
                print("✅ Pipeline content restored after refresh")
            else:
                print("❌ Pipeline content not restored after refresh")

            # Check for restoration indicator
            restoration_indicator = "Restored" in restored_title or "restored" in driver.page_source.lower()
            if restoration_indicator:
                print("✅ Restoration indicator found")
            else:
                print("⚠️ No restoration indicator found")

        except Exception as e:
            print(f"❌ Error checking restored content: {e}")
            content_restored = False

        # Take screenshot after refresh
        screenshot_path_refresh = os.path.join(os.path.dirname(__file__), 'screenshots', 'pipeline_after_refresh_20250618.png')
        driver.save_screenshot(screenshot_path_refresh)
        print(f"📸 Screenshot after refresh saved: {screenshot_path_refresh}")

        # Test 3: Verify console logs and errors
        print("\n🐛 Test 3: Checking console logs...")

        logs = driver.get_log('browser')
        errors = [log for log in logs if log['level'] == 'SEVERE']
        warnings = [log for log in logs if log['level'] == 'WARNING']

        print(f"📊 Console logs: {len(errors)} errors, {len(warnings)} warnings")

        if errors:
            print("❌ Console errors found:")
            for error in errors[:5]:  # Show first 5 errors
                print(f"   - {error['message']}")
        else:
            print("✅ No console errors found")

        # Generate test report
        test_results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "pipeline_window_opened": pipeline_opened,
            "step_cards_found": len(step_cards),
            "styled_buttons": styled_buttons,
            "total_buttons": len(buttons),
            "content_restored_after_refresh": content_restored,
            "restoration_indicator_found": restoration_indicator,
            "console_errors": len(errors),
            "console_warnings": len(warnings),
            "screenshots": [screenshot_path, screenshot_path_refresh]
        }

        # Save test report
        report_path = os.path.join(os.path.dirname(__file__), 'screenshots', 'pipeline_test_report.json')
        with open(report_path, 'w') as f:
            json.dump(test_results, f, indent=2)

        print(f"\n📄 Test report saved: {report_path}")

        # Summary
        print("\n📊 TEST SUMMARY")
        print("=" * 40)
        print(f"✅ Pipeline window opened: {pipeline_opened}")
        print(f"✅ Step cards found: {len(step_cards)}")
        print(f"✅ Styled buttons: {styled_buttons}/{len(buttons)}")
        print(f"✅ Content restored: {content_restored}")
        print(f"✅ Restoration indicator: {restoration_indicator}")
        print(f"✅ Console clean: {len(errors) == 0}")

        success = (pipeline_opened and content_restored and len(errors) == 0)
        print(f"\n🎯 Overall test result: {'PASS' if success else 'FAIL'}")

        return success

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    success = test_pipeline_window_styling_and_refresh()
    sys.exit(0 if success else 1)
