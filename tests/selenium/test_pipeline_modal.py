#!/usr/bin/env python3
"""
Test Pipeline Modal Functionality
Tests the conversion from pipeline window to full-screen modal
"""

import os
import sys
import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def setup_chrome_driver():
    """Set up Chrome driver with extension loaded"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")

    # Load extension
    extension_path = os.path.join(os.path.dirname(__file__), '..', '..', 'dist', 'dev')
    chrome_options.add_argument(f"--load-extension={extension_path}")

    # Use Chrome binary if available
    chrome_binary = os.path.join(os.path.dirname(__file__), 'chrome-135', 'chrome-linux64', 'chrome')
    if os.path.exists(chrome_binary):
        chrome_options.binary_location = chrome_binary

    driver = webdriver.Chrome(options=chrome_options)
    return driver

def test_pipeline_modal():
    """Test pipeline modal functionality"""
    driver = setup_chrome_driver()

    try:
        print("🧪 PIPELINE MODAL FUNCTIONALITY TEST")
        print("=" * 60)

        # Get extension ID
        driver.get("chrome://extensions/")
        time.sleep(2)

        # Enable developer mode
        try:
            dev_mode_toggle = driver.find_element(By.CSS_SELECTOR, "#devMode")
            if not dev_mode_toggle.is_selected():
                dev_mode_toggle.click()
                time.sleep(1)
        except:
            pass

        # Get extension ID from management API
        extension_id = driver.execute_script("""
            return new Promise((resolve) => {
                chrome.management.getAll((extensions) => {
                    const mvat = extensions.find(ext => ext.name.includes('MVAT'));
                    resolve(mvat ? mvat.id : null);
                });
            });
        """)

        if not extension_id:
            raise Exception("MVAT extension not found")

        print(f"✅ Extension ID: {extension_id}")

        # Navigate to popup
        popup_url = f"chrome-extension://{extension_id}/popup.html"
        driver.get(popup_url)
        time.sleep(3)

        print("✅ Popup loaded")

        # Navigate to uploads tab
        try:
            # First try to find navigation tabs
            nav_tabs = driver.find_elements(By.CSS_SELECTOR, "button, a, [role='tab']")
            print(f"📝 Found {len(nav_tabs)} navigation elements")

            # Look for uploads tab specifically
            uploads_tab = None
            for tab in nav_tabs:
                tab_text = tab.get_attribute('textContent') or tab.get_attribute('innerText') or ''
                if 'upload' in tab_text.lower():
                    uploads_tab = tab
                    break

            if uploads_tab:
                uploads_tab.click()
                time.sleep(2)
                print("✅ Navigated to uploads tab")
            else:
                print("⚠️ Could not find uploads tab, checking all tabs...")
                for i, tab in enumerate(nav_tabs[:5]):  # Check first 5 tabs
                    tab_text = tab.get_attribute('textContent') or ''
                    print(f"   Tab {i+1}: '{tab_text.strip()}'")
                    if i == 1:  # Try clicking second tab (often uploads)
                        tab.click()
                        time.sleep(2)
                        print(f"✅ Clicked tab {i+1}: '{tab_text.strip()}'")
                        break
        except Exception as e:
            print(f"⚠️ Navigation error: {str(e)}")

        # Look for pipeline buttons
        pipeline_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Pipeline')]")

        if not pipeline_buttons:
            print("⚠️ No pipeline buttons found, checking for sample data...")

            # Check if there are any file cards or upload areas
            file_cards = driver.find_elements(By.CSS_SELECTOR, "[class*='card'], [class*='file'], [class*='invoice']")
            print(f"📝 Found {len(file_cards)} potential file elements")

            # Try to find drag-drop area
            drag_areas = driver.find_elements(By.CSS_SELECTOR, "[class*='drag'], [class*='drop'], [class*='upload']")
            print(f"📝 Found {len(drag_areas)} drag-drop areas")

            if drag_areas:
                print("✅ Upload interface found - modal functionality ready")
            else:
                print("⚠️ No upload interface found")
        else:
            print(f"✅ Found {len(pipeline_buttons)} pipeline buttons")

            # Click first pipeline button to test modal
            first_button = pipeline_buttons[0]
            print("🔄 Clicking pipeline button to test modal...")

            # Take screenshot before click
            screenshot_path = f"tests/selenium/screenshots/before_modal_click_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")

            first_button.click()
            time.sleep(2)

            # Check for modal
            modal_elements = driver.find_elements(By.CSS_SELECTOR, "[class*='modal'], [class*='fixed'], [class*='z-50']")

            if modal_elements:
                print(f"✅ Modal detected! Found {len(modal_elements)} modal elements")

                # Take screenshot of modal
                screenshot_path = f"tests/selenium/screenshots/modal_open_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                driver.save_screenshot(screenshot_path)
                print(f"📸 Modal screenshot saved: {screenshot_path}")

                # Look for modal content
                modal_content = driver.find_elements(By.XPATH, "//div[contains(text(), 'Pipeline') or contains(text(), 'Processing')]")
                if modal_content:
                    print("✅ Modal content found - pipeline modal working!")

                # Try to close modal with escape key
                ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                time.sleep(1)

                # Check if modal closed
                modal_elements_after = driver.find_elements(By.CSS_SELECTOR, "[class*='modal'], [class*='fixed'], [class*='z-50']")
                if len(modal_elements_after) < len(modal_elements):
                    print("✅ Modal closed with escape key")
                else:
                    print("⚠️ Modal did not close with escape key")

                    # Try clicking close button
                    close_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Close') or contains(text(), '✕')]")
                    if close_buttons:
                        close_buttons[0].click()
                        time.sleep(1)
                        print("✅ Modal closed with close button")

            else:
                print("⚠️ No modal detected after clicking pipeline button")

                # Check if a new window opened (old behavior)
                if len(driver.window_handles) > 1:
                    print("⚠️ New window opened instead of modal - old behavior detected")
                    driver.close()
                    driver.switch_to.window(driver.window_handles[0])
                else:
                    print("✅ No new window opened - modal behavior expected")

        # Final screenshot
        screenshot_path = f"tests/selenium/screenshots/pipeline_modal_test_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        driver.save_screenshot(screenshot_path)
        print(f"📸 Final screenshot saved: {screenshot_path}")

        print("✅ Pipeline modal test completed")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        screenshot_path = f"tests/selenium/screenshots/pipeline_modal_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        driver.save_screenshot(screenshot_path)
        print(f"📸 Error screenshot saved: {screenshot_path}")

    finally:
        driver.quit()

if __name__ == "__main__":
    test_pipeline_modal()
