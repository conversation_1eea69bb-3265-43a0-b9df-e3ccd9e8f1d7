#!/usr/bin/env node

/**
 * Comprehensive Selenium Test for Pipeline Modal Styling
 * Tests the pipeline modal layout, console log display, and overall styling
 */

import { Builder, By, until, Key } from 'selenium-webdriver';
import chrome from 'selenium-webdriver/chrome.js';
import fs from 'fs';
import path from 'path';

const EXTENSION_PATH = path.resolve('./dist/dev');
const SCREENSHOT_DIR = path.resolve('./tests/screenshots');
const TEST_PDF = path.resolve('./data/samples/input/Pipeline-327_K_06_23_PCM.pdf');

// Ensure screenshot directory exists
if (!fs.existsSync(SCREENSHOT_DIR)) {
  fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
}

console.log('🧪 SELENIUM PIPELINE MODAL STYLING TEST');
console.log('============================================================');
console.log('📅 Timestamp:', new Date().toISOString());
console.log('🎯 Purpose: Test pipeline modal layout and console styling');
console.log('📁 Extension Path:', EXTENSION_PATH);
console.log('📄 Test PDF:', TEST_PDF);
console.log('============================================================\n');

/**
 * Setup Chrome driver with extension
 */
async function setupDriver() {
  console.log('🔧 Setting up Chrome driver with extension...');

  const options = new chrome.Options();
  options.addArguments(`--load-extension=${EXTENSION_PATH}`);
  options.addArguments('--no-sandbox');
  options.addArguments('--disable-dev-shm-usage');
  options.addArguments('--disable-web-security');
  options.addArguments('--allow-running-insecure-content');
  options.addArguments('--window-size=1920,1080');

  if (process.env.SELENIUM_HEADLESS === 'true') {
    options.addArguments('--headless');
  }

  const driver = await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .build();

  console.log('✅ Chrome driver setup complete');
  return driver;
}

/**
 * Get extension ID from Chrome
 */
async function getExtensionId(driver) {
  console.log('🔍 Finding extension ID...');

  await driver.get('chrome://extensions/');
  await driver.sleep(2000);

  // Enable developer mode if not already enabled
  try {
    const devModeToggle = await driver.findElement(By.css('#devMode'));
    const isEnabled = await devModeToggle.isSelected();
    if (!isEnabled) {
      await devModeToggle.click();
      await driver.sleep(1000);
    }
  } catch (error) {
    console.log('⚠️ Could not find developer mode toggle, continuing...');
  }

  // Find extension ID
  const extensionCards = await driver.findElements(By.css('extensions-item'));
  for (const card of extensionCards) {
    try {
      const nameElement = await card.findElement(By.css('#name'));
      const name = await nameElement.getText();
      if (name.includes('MVAT') || name.includes('Invoice')) {
        const idElement = await card.findElement(By.css('#extension-id'));
        const extensionId = await idElement.getText();
        console.log('✅ Extension ID found:', extensionId);
        return extensionId.replace('ID: ', '');
      }
    } catch (error) {
      continue;
    }
  }

  throw new Error('Extension not found');
}

/**
 * Test pipeline modal opening and basic layout
 */
async function testPipelineModalBasicLayout(driver, extensionId) {
  console.log('🔧 Test 1: Pipeline Modal Basic Layout');

  try {
    // Open extension popup
    await driver.get(`chrome-extension://${extensionId}/popup.html`);
    await driver.sleep(2000);

    // Take screenshot of initial state
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '01-extension-popup.png'), data, 'base64');
    });

    // Navigate to uploads tab
    const uploadsTab = await driver.wait(until.elementLocated(By.css('[data-testid="uploads-tab"], .tab-uploads, [role="tab"]:nth-child(2)')), 10000);
    await uploadsTab.click();
    await driver.sleep(1000);

    // Look for pipeline button or trigger
    const pipelineButton = await driver.wait(until.elementLocated(By.css('[data-testid="run-pipeline"], .run-pipeline, button:contains("Pipeline")')), 10000);
    await pipelineButton.click();
    await driver.sleep(2000);

    // Check if modal opened
    const modal = await driver.wait(until.elementLocated(By.css('.fixed.inset-0, [role="dialog"], .modal')), 10000);
    const isDisplayed = await modal.isDisplayed();

    if (!isDisplayed) {
      throw new Error('Pipeline modal did not open');
    }

    console.log('✅ Pipeline modal opened successfully');

    // Take screenshot of modal
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '02-pipeline-modal-opened.png'), data, 'base64');
    });

    return true;

  } catch (error) {
    console.error('❌ Test 1 FAILED:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '02-pipeline-modal-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Test pipeline modal layout proportions
 */
async function testPipelineModalProportions(driver) {
  console.log('🔧 Test 2: Pipeline Modal Layout Proportions');

  try {
    // Find the main content container
    const mainContent = await driver.findElement(By.css('.flex-1.flex.overflow-hidden'));
    const leftSide = await driver.findElement(By.css('.w-2\\/5'));
    const rightSide = await driver.findElement(By.css('.w-3\\/5'));

    // Get dimensions
    const mainRect = await mainContent.getRect();
    const leftRect = await leftSide.getRect();
    const rightRect = await rightSide.getRect();

    console.log('📏 Modal dimensions:');
    console.log(`   Main content: ${mainRect.width}x${mainRect.height}`);
    console.log(`   Left side: ${leftRect.width}x${leftRect.height}`);
    console.log(`   Right side: ${rightRect.width}x${rightRect.height}`);

    // Calculate proportions
    const leftProportion = leftRect.width / mainRect.width;
    const rightProportion = rightRect.width / mainRect.width;

    console.log('📊 Layout proportions:');
    console.log(`   Left side: ${(leftProportion * 100).toFixed(1)}%`);
    console.log(`   Right side: ${(rightProportion * 100).toFixed(1)}%`);

    // Verify proportions are approximately 40/60
    if (Math.abs(leftProportion - 0.4) > 0.1) {
      throw new Error(`Left side proportion incorrect: expected ~40%, got ${(leftProportion * 100).toFixed(1)}%`);
    }

    if (Math.abs(rightProportion - 0.6) > 0.1) {
      throw new Error(`Right side proportion incorrect: expected ~60%, got ${(rightProportion * 100).toFixed(1)}%`);
    }

    console.log('✅ Layout proportions are correct (40/60 split)');

    // Take screenshot with measurements
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '03-pipeline-modal-proportions.png'), data, 'base64');
    });

    return true;

  } catch (error) {
    console.error('❌ Test 2 FAILED:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '03-pipeline-modal-proportions-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Test console log area styling and space utilization
 */
async function testConsoleLogStyling(driver) {
  console.log('🔧 Test 3: Console Log Area Styling');

  try {
    // Find console log container
    const consoleContainer = await driver.findElement(By.css('.w-3\\/5.flex.flex-col.min-h-0'));
    const consoleLogger = await driver.findElement(By.css('.bg-gray-900.text-green-400.font-mono'));

    // Get console dimensions
    const containerRect = await consoleContainer.getRect();
    const loggerRect = await consoleLogger.getRect();

    console.log('📏 Console dimensions:');
    console.log(`   Container: ${containerRect.width}x${containerRect.height}`);
    console.log(`   Logger: ${loggerRect.width}x${loggerRect.height}`);

    // Check if console logger fills the container
    const widthUtilization = loggerRect.width / containerRect.width;
    const heightUtilization = loggerRect.height / containerRect.height;

    console.log('📊 Console space utilization:');
    console.log(`   Width: ${(widthUtilization * 100).toFixed(1)}%`);
    console.log(`   Height: ${(heightUtilization * 100).toFixed(1)}%`);

    // Verify console uses most of the available space
    if (widthUtilization < 0.9) {
      throw new Error(`Console width utilization too low: ${(widthUtilization * 100).toFixed(1)}%`);
    }

    if (heightUtilization < 0.8) {
      throw new Error(`Console height utilization too low: ${(heightUtilization * 100).toFixed(1)}%`);
    }

    console.log('✅ Console log area utilizes space effectively');

    // Check console styling
    const consoleStyles = await driver.executeScript(`
      const console = document.querySelector('.bg-gray-900.text-green-400.font-mono');
      const styles = window.getComputedStyle(console);
      return {
        backgroundColor: styles.backgroundColor,
        color: styles.color,
        fontFamily: styles.fontFamily,
        height: styles.height,
        overflow: styles.overflow
      };
    `);

    console.log('🎨 Console styling:');
    console.log(`   Background: ${consoleStyles.backgroundColor}`);
    console.log(`   Text color: ${consoleStyles.color}`);
    console.log(`   Font: ${consoleStyles.fontFamily}`);
    console.log(`   Height: ${consoleStyles.height}`);
    console.log(`   Overflow: ${consoleStyles.overflow}`);

    // Take screenshot of console area
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '04-console-log-styling.png'), data, 'base64');
    });

    return true;

  } catch (error) {
    console.error('❌ Test 3 FAILED:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '04-console-log-styling-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Test pipeline steps layout and styling
 */
async function testPipelineStepsLayout(driver) {
  console.log('🔧 Test 4: Pipeline Steps Layout');

  try {
    // Find pipeline steps container
    const stepsContainer = await driver.findElement(By.css('.w-2\\/5'));
    const stepCards = await driver.findElements(By.css('[data-testid="pipeline-step"], .pipeline-step, .space-y-3 > div'));

    console.log(`📋 Found ${stepCards.length} pipeline step cards`);

    if (stepCards.length === 0) {
      throw new Error('No pipeline step cards found');
    }

    // Check step card styling
    for (let i = 0; i < Math.min(stepCards.length, 3); i++) {
      const card = stepCards[i];
      const cardRect = await card.getRect();

      console.log(`📏 Step ${i + 1} dimensions: ${cardRect.width}x${cardRect.height}`);

      // Check if card is visible and has reasonable dimensions
      if (cardRect.width < 100 || cardRect.height < 50) {
        throw new Error(`Step ${i + 1} card too small: ${cardRect.width}x${cardRect.height}`);
      }
    }

    console.log('✅ Pipeline steps have proper layout and dimensions');

    // Take screenshot of steps area
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '05-pipeline-steps-layout.png'), data, 'base64');
    });

    return true;

  } catch (error) {
    console.error('❌ Test 4 FAILED:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '05-pipeline-steps-layout-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Test modal responsiveness and overall styling
 */
async function testModalResponsiveness(driver) {
  console.log('🔧 Test 5: Modal Responsiveness and Overall Styling');

  try {
    // Get modal dimensions
    const modal = await driver.findElement(By.css('.bg-white.rounded-lg.shadow-2xl'));
    const modalRect = await modal.getRect();

    console.log(`📏 Modal dimensions: ${modalRect.width}x${modalRect.height}`);

    // Check if modal is reasonably sized
    if (modalRect.width < 800 || modalRect.height < 600) {
      throw new Error(`Modal too small: ${modalRect.width}x${modalRect.height}`);
    }

    // Test modal styling
    const modalStyles = await driver.executeScript(`
      const modal = document.querySelector('.bg-white.rounded-lg.shadow-2xl');
      const styles = window.getComputedStyle(modal);
      return {
        backgroundColor: styles.backgroundColor,
        borderRadius: styles.borderRadius,
        boxShadow: styles.boxShadow,
        display: styles.display,
        flexDirection: styles.flexDirection
      };
    `);

    console.log('🎨 Modal styling:');
    console.log(`   Background: ${modalStyles.backgroundColor}`);
    console.log(`   Border radius: ${modalStyles.borderRadius}`);
    console.log(`   Display: ${modalStyles.display}`);
    console.log(`   Flex direction: ${modalStyles.flexDirection}`);

    // Check header styling
    const header = await driver.findElement(By.css('.flex.items-center.justify-between.px-4.py-3'));
    const headerRect = await header.getRect();

    console.log(`📏 Header dimensions: ${headerRect.width}x${headerRect.height}`);

    if (headerRect.height > 80) {
      throw new Error(`Header too tall: ${headerRect.height}px`);
    }

    console.log('✅ Modal has proper responsiveness and styling');

    // Take final screenshot
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '06-modal-responsiveness.png'), data, 'base64');
    });

    return true;

  } catch (error) {
    console.error('❌ Test 5 FAILED:', error.message);
    await driver.takeScreenshot().then(data => {
      fs.writeFileSync(path.join(SCREENSHOT_DIR, '06-modal-responsiveness-error.png'), data, 'base64');
    });
    return false;
  }
}

/**
 * Main test runner
 */
async function runPipelineModalTests() {
  let driver;

  try {
    driver = await setupDriver();
    const extensionId = await getExtensionId(driver);

    const tests = [
      () => testPipelineModalBasicLayout(driver, extensionId),
      () => testPipelineModalProportions(driver),
      () => testConsoleLogStyling(driver),
      () => testPipelineStepsLayout(driver),
      () => testModalResponsiveness(driver)
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
      await driver.sleep(1000); // Brief pause between tests
    }

    console.log('\n============================================================');
    console.log('📊 PIPELINE MODAL STYLING TEST RESULTS');
    console.log('============================================================');
    console.log(`✅ Tests Passed: ${passed}`);
    console.log(`❌ Tests Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    console.log(`📸 Screenshots saved to: ${SCREENSHOT_DIR}`);
    console.log('============================================================');

    if (failed === 0) {
      console.log('🎉 ALL PIPELINE MODAL TESTS PASSED!');
    } else {
      console.log('❌ SOME TESTS FAILED - REVIEW STYLING IMPLEMENTATION');
    }

  } catch (error) {
    console.error('💥 Test runner failed:', error);
  } finally {
    if (driver) {
      await driver.quit();
    }
  }
}

// Run tests
runPipelineModalTests().catch(error => {
  console.error('💥 Pipeline modal test failed:', error);
  process.exit(1);
});
