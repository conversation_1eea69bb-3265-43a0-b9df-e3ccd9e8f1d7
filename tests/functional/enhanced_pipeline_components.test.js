/**
 * Enhanced Pipeline Components Functional Tests
 * Tests the new multi-layout pipeline visualization components
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  X: () => <div data-testid="x-icon">X</div>,
  Maximize2: () => <div data-testid="maximize-icon">Maximize</div>,
  Minimize2: () => <div data-testid="minimize-icon">Minimize</div>,
  PanelRight: () => <div data-testid="panel-right-icon">Panel</div>,
  Fullscreen: () => <div data-testid="fullscreen-icon">Fullscreen</div>,
  Play: () => <div data-testid="play-icon">Play</div>,
  Pause: () => <div data-testid="pause-icon">Pause</div>,
  RotateCcw: () => <div data-testid="rotate-icon">Rotate</div>
}));

// Mock pipeline dependencies
vi.mock('../../../src/core/config/pipelineSteps.js', () => ({
  PIPELINE_STEPS: [
    {
      id: 'pdf_extraction',
      name: 'PDF Text Extraction',
      description: 'Extract text from PDF',
      icon: '📄',
      color: 'blue'
    },
    {
      id: 'deepseek_analysis',
      name: 'DeepSeek AI Analysis',
      description: 'AI-powered document analysis',
      icon: '🤖',
      color: 'purple'
    }
  ],
  STEP_STATUS: {
    PENDING: 'pending',
    RUNNING: 'running',
    COMPLETED: 'completed',
    ERROR: 'error',
    SKIPPED: 'skipped'
  },
  getNextAvailableSteps: vi.fn(() => [])
}));

vi.mock('../../../src/services/DocumentProcessingPipeline.js', () => ({
  documentProcessingPipeline: {
    runPdfExtraction: vi.fn(() => Promise.resolve({ success: true, data: 'extracted text' })),
    runDeepSeekAnalysis: vi.fn(() => Promise.resolve({ success: true, data: 'analysis result' })),
    runRagDocumentLinking: vi.fn(() => Promise.resolve({ success: true, data: 'rag result' })),
    runOcrStructuralReference: vi.fn(() => Promise.resolve({ success: true, data: 'ocr result' })),
    runFinalOutputGeneration: vi.fn(() => Promise.resolve({ success: true, data: 'final result' }))
  }
}));

// Mock child components
vi.mock('../../../src/components/features/pipeline/PipelineStepCard.jsx', () => ({
  default: ({ step, status, onAction, isActive }) => (
    <div data-testid={`step-card-${step.id}`} className={isActive ? 'active' : ''}>
      <span>{step.icon} {step.name}</span>
      <span data-testid={`status-${step.id}`}>{status || 'pending'}</span>
      <button onClick={() => onAction?.(step.id, 'rerun')}>Rerun</button>
    </div>
  )
}));

vi.mock('../../../src/components/features/pipeline/LiveConsoleLogger.jsx', () => ({
  default: ({ logs, isProcessing, onClear, className }) => (
    <div data-testid="live-console-logger" className={className}>
      <div>Processing: {isProcessing ? 'true' : 'false'}</div>
      <div>Logs: {logs.length}</div>
      <button onClick={onClear}>Clear Logs</button>
    </div>
  )
}));

// Import the component to test
import EnhancedPipelineVisualization, { LAYOUT_MODES } from '../../../src/components/features/pipeline/EnhancedPipelineVisualization.jsx';

describe('EnhancedPipelineVisualization', () => {
  const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });

  const defaultProps = {
    file: mockFile,
    isProcessing: false,
    onProcessingChange: vi.fn(),
    onStepComplete: vi.fn(),
    onError: vi.fn(),
    autoRun: false,
    initialLayout: LAYOUT_MODES.COMPACT
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window environment variables
    global.window = {
      ...global.window,
      __MVAT_ENV__: {
        DEEPSEEK_API_KEY: 'test-api-key',
        COMPANY_NAME: 'Test Company'
      }
    };
  });

  test('renders compact layout by default', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} />);

    expect(screen.getByText('Multi-Step Pipeline')).toBeInTheDocument();
    expect(screen.getByTestId('minimize-icon')).toBeInTheDocument();
    expect(screen.getByTestId('panel-right-icon')).toBeInTheDocument();
    expect(screen.getByTestId('fullscreen-icon')).toBeInTheDocument();
  });

  test('displays pipeline steps in compact mode', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} />);

    expect(screen.getByText('📄 PDF Text Extraction')).toBeInTheDocument();
    expect(screen.getByText('🤖 DeepSeek AI Analysis')).toBeInTheDocument();
  });

  test('shows run pipeline button when file is provided', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} />);

    const runButton = screen.getByRole('button', { name: /🚀/i });
    expect(runButton).toBeInTheDocument();
    expect(runButton).not.toBeDisabled();
  });

  test('disables run button when processing', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} isProcessing={true} />);

    const runButton = screen.getByRole('button', { name: /⏳/i });
    expect(runButton).toBeInTheDocument();
    expect(runButton).toBeDisabled();
  });

  test('shows progress bar when processing', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} isProcessing={true} />);

    const progressBar = screen.getByRole('progressbar', { hidden: true });
    expect(progressBar).toBeInTheDocument();
  });

  test('renders right panel layout when specified', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} initialLayout={LAYOUT_MODES.RIGHT_PANEL} />);

    // Should render the right panel layout
    expect(screen.getByText('Multi-Step Processing Pipeline')).toBeInTheDocument();
    expect(screen.getByText('Pipeline Steps')).toBeInTheDocument();
    expect(screen.getByTestId('live-console-logger')).toBeInTheDocument();
  });

  test('renders full screen layout when specified', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} initialLayout={LAYOUT_MODES.FULL_SCREEN} />);

    // Should render the full screen modal layout
    expect(screen.getByText('Multi-Step Processing Pipeline')).toBeInTheDocument();
    expect(screen.getByText('Live Console Logs')).toBeInTheDocument();
    expect(screen.getByText('Real-time processing logs with full vertical space')).toBeInTheDocument();
  });

  test('handles layout mode changes', async () => {
    const { rerender } = render(<EnhancedPipelineVisualization {...defaultProps} />);

    // Start in compact mode
    expect(screen.getByText('Multi-Step Pipeline')).toBeInTheDocument();

    // Change to right panel mode
    rerender(<EnhancedPipelineVisualization {...defaultProps} initialLayout={LAYOUT_MODES.RIGHT_PANEL} />);
    expect(screen.getByText('Pipeline Steps')).toBeInTheDocument();

    // Change to full screen mode
    rerender(<EnhancedPipelineVisualization {...defaultProps} initialLayout={LAYOUT_MODES.FULL_SCREEN} />);
    expect(screen.getByText('Live Console Logs')).toBeInTheDocument();
  });

  test('adds console logs during processing', async () => {
    const { rerender } = render(<EnhancedPipelineVisualization {...defaultProps} />);

    // Start processing
    rerender(<EnhancedPipelineVisualization {...defaultProps} isProcessing={true} />);

    // Console logger should show processing state
    expect(screen.getByText('Processing: true')).toBeInTheDocument();
  });

  test('handles step actions', async () => {
    render(<EnhancedPipelineVisualization {...defaultProps} />);

    // Find and click a rerun button
    const rerunButton = screen.getAllByText('Rerun')[0];
    fireEvent.click(rerunButton);

    // Should handle the action (no errors thrown)
    expect(rerunButton).toBeInTheDocument();
  });

  test('exports console logs functionality', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} initialLayout={LAYOUT_MODES.FULL_SCREEN} />);

    // Should have live console logger with export capability
    expect(screen.getByTestId('live-console-logger')).toBeInTheDocument();
  });

  test('handles escape key in full screen mode', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} initialLayout={LAYOUT_MODES.FULL_SCREEN} />);

    // Simulate escape key press
    fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' });

    // Should handle the escape key (no errors thrown)
    expect(screen.getByText('Live Console Logs')).toBeInTheDocument();
  });

  test('calculates overall progress correctly', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} isProcessing={true} />);

    // Progress bar should be present
    const progressBar = screen.getByRole('progressbar', { hidden: true });
    expect(progressBar).toBeInTheDocument();
  });

  test('handles API key loading', () => {
    render(<EnhancedPipelineVisualization {...defaultProps} />);

    // Should handle API key from environment
    expect(global.window.__MVAT_ENV__.DEEPSEEK_API_KEY).toBe('test-api-key');
  });
});

describe('LAYOUT_MODES', () => {
  test('exports correct layout mode constants', () => {
    expect(LAYOUT_MODES.COMPACT).toBe('compact');
    expect(LAYOUT_MODES.RIGHT_PANEL).toBe('right_panel');
    expect(LAYOUT_MODES.FULL_SCREEN).toBe('full_screen');
  });
});
