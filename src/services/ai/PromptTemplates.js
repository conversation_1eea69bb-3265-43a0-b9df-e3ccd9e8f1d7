/**
 * Enhanced Prompt Templates Service
 * Provides optimized prompts for 90% accuracy target
 *
 * ASSIGNMENT-105: Enhanced DeepSeek Analysis Integration
 * Epic: EPIC-008 - Enhanced AI Processing (90% Accuracy Target)
 */

import { ENHANCED_EXTRACTION_PATTERNS, DOCUMENT_KIND_FIELDS } from '../../core/config/fieldDefinitions.js';
import { DOCUMENT_TYPE_DESCRIPTIONS } from '../../core/config/documentTypes.js';

export class PromptTemplates {
  constructor() {
    this.templateCache = new Map();
  }

  /**
   * Generate enhanced extraction prompt for basic pass
   * @param {string} text - Document text
   * @param {Object} options - Extraction options
   * @returns {Object} - Prompt and system prompt
   */
  generateBasicExtractionPrompt(text, options = {}) {
    const documentType = options.documentType || 'vat';
    const language = options.language || 'pl';
    const companyInfo = options.companyInfo || {};

    const cacheKey = `basic_${documentType}_${language}`;
    if (this.templateCache.has(cacheKey)) {
      const template = this.templateCache.get(cacheKey);
      return {
        prompt: template.prompt.replace('{{DOCUMENT_TEXT}}', text.substring(0, 3000)),
        systemPrompt: template.systemPrompt
      };
    }

    const relevantFields = this.getRelevantFields(documentType);
    const fieldDescriptions = this.generateFieldDescriptions(relevantFields, language);

    const prompt = `Extract structured data from this ${DOCUMENT_TYPE_DESCRIPTIONS[documentType] || documentType} document.

DOCUMENT LANGUAGE: ${language}
DOCUMENT TYPE: ${documentType}
EXTRACTION ACCURACY TARGET: 90%

REQUIRED FIELDS TO EXTRACT:
${fieldDescriptions}

EXTRACTION GUIDELINES:
1. Extract ALL visible fields with maximum accuracy
2. Provide confidence scores (0.0-1.0) for each field
3. Include source text snippets for verification
4. Use exact field names as specified above
5. Maintain original language for field values
6. Mark uncertain extractions with lower confidence

RESPONSE FORMAT (JSON):
{
  "fields": {
    "field_name": {
      "value": "extracted_value",
      "confidence": 0.85,
      "source_text": "text_where_found",
      "extraction_method": "direct_match|pattern_match|context_inference"
    }
  },
  "metadata": {
    "document_type": "detected_type",
    "language": "detected_language",
    "extraction_method": "basic_pass",
    "total_fields_found": 0,
    "high_confidence_fields": 0
  }
}

DOCUMENT TEXT:
{{DOCUMENT_TEXT}}`;

    const systemPrompt = `You are an expert document analysis AI specializing in ${DOCUMENT_TYPE_DESCRIPTIONS[documentType] || documentType} processing with 90% accuracy target.

CORE RESPONSIBILITIES:
- Extract structured data with maximum precision
- Provide accurate confidence scoring
- Maintain field name consistency
- Preserve original language in values
- Focus on critical business fields first

ACCURACY REQUIREMENTS:
- Critical fields (amounts, dates, IDs): 95%+ accuracy
- High importance fields: 90%+ accuracy
- Medium importance fields: 85%+ accuracy
- All fields: Conservative confidence scoring

EXTRACTION STRATEGY:
1. Scan document systematically for each required field
2. Use multiple detection methods (direct text, patterns, context)
3. Cross-reference related fields for validation
4. Apply business logic for reasonableness checks
5. Provide detailed source references for verification

Be thorough, accurate, and conservative in confidence scoring.`;

    const template = { prompt, systemPrompt };
    this.templateCache.set(cacheKey, template);

    return {
      prompt: prompt.replace('{{DOCUMENT_TEXT}}', text.substring(0, 3000)),
      systemPrompt
    };
  }

  /**
   * Generate critical validation prompt for validation pass
   * @param {string} text - Document text
   * @param {Array} criticalFields - Fields to validate
   * @param {Object} options - Validation options
   * @returns {Object} - Prompt and system prompt
   */
  generateCriticalValidationPrompt(text, criticalFields, options = {}) {
    const documentType = options.documentType || 'vat';
    const language = options.language || 'pl';

    const fieldsToValidate = criticalFields.map(field =>
      `- ${field.name}: "${field.value}" (confidence: ${field.confidence})`
    ).join('\n');

    const validationRules = this.generateValidationRules(criticalFields);

    const prompt = `CRITICAL FIELD VALIDATION - 95% ACCURACY REQUIRED

Re-examine these critical fields extracted from the ${documentType} document. Apply rigorous validation and provide corrections if needed.

FIELDS TO VALIDATE:
${fieldsToValidate}

VALIDATION RULES:
${validationRules}

VALIDATION PROCESS:
1. Re-scan the document text for each field
2. Apply pattern matching and format validation
3. Check business logic consistency
4. Verify against document context
5. Provide corrections with detailed reasoning

RESPONSE FORMAT (JSON):
{
  "fields": {
    "field_name": {
      "value": "validated_value",
      "confidence": 0.95,
      "validation_status": "confirmed|corrected|uncertain",
      "correction_reason": "detailed_reason_if_corrected",
      "validation_checks": {
        "format_valid": true,
        "pattern_match": true,
        "business_logic": true,
        "context_consistent": true
      }
    }
  },
  "validations": {
    "field_name": {
      "original_value": "original",
      "validated_value": "validated",
      "confidence_change": 0.1,
      "validation_notes": "detailed_validation_notes",
      "correction_applied": false
    }
  },
  "validation_summary": {
    "fields_validated": 0,
    "fields_corrected": 0,
    "average_confidence": 0.0,
    "validation_score": 0.0
  }
}

DOCUMENT TEXT:
${text.substring(0, 3000)}`;

    const systemPrompt = `You are a specialized validation AI with expertise in critical field accuracy for ${documentType} documents.

VALIDATION MISSION:
- Achieve 95%+ accuracy for critical fields
- Apply rigorous validation standards
- Correct errors with detailed explanations
- Maintain conservative confidence scoring
- Ensure business logic compliance

VALIDATION METHODOLOGY:
1. Multi-pattern field detection
2. Format and structure validation
3. Business rule compliance checking
4. Cross-field consistency verification
5. Context-aware error correction

CRITICAL FIELD PRIORITIES:
- Financial amounts and calculations
- Tax identification numbers
- Legal entity names and addresses
- Document numbers and dates
- Payment terms and conditions

Be extremely thorough and precise. Critical fields must meet the highest accuracy standards.`;

    return { prompt, systemPrompt };
  }

  /**
   * Generate confidence enhancement prompt for enhancement pass
   * @param {string} text - Document text
   * @param {Array} lowConfidenceFields - Fields to enhance
   * @param {Object} options - Enhancement options
   * @returns {Object} - Prompt and system prompt
   */
  generateConfidenceEnhancementPrompt(text, lowConfidenceFields, options = {}) {
    const documentType = options.documentType || 'vat';

    const fieldsToEnhance = lowConfidenceFields.map(field =>
      `- ${field.name}: "${field.value}" (confidence: ${field.confidence})`
    ).join('\n');

    const enhancementStrategies = this.generateEnhancementStrategies(lowConfidenceFields);

    const prompt = `CONFIDENCE ENHANCEMENT - TARGET 90% ACCURACY

Improve the accuracy and confidence of these low-confidence fields using advanced extraction techniques.

LOW CONFIDENCE FIELDS:
${fieldsToEnhance}

ENHANCEMENT STRATEGIES:
${enhancementStrategies}

ENHANCEMENT TECHNIQUES:
1. Alternative pattern matching
2. Context-based inference
3. Cross-reference validation
4. Fuzzy matching algorithms
5. Business logic application

RESPONSE FORMAT (JSON):
{
  "fields": {
    "field_name": {
      "value": "enhanced_value",
      "confidence": 0.90,
      "enhancement_method": "pattern_matching|context_analysis|cross_reference|fuzzy_match",
      "improvement_notes": "detailed_explanation_of_improvement",
      "alternative_values": ["other_possible_values"],
      "enhancement_score": 0.25
    }
  },
  "enhancements": {
    "field_name": {
      "original_confidence": 0.65,
      "enhanced_confidence": 0.90,
      "confidence_gain": 0.25,
      "enhancement_reason": "detailed_explanation",
      "techniques_used": ["pattern_matching", "context_analysis"]
    }
  },
  "enhancement_summary": {
    "fields_enhanced": 0,
    "average_confidence_gain": 0.0,
    "enhancement_success_rate": 0.0
  }
}

DOCUMENT TEXT:
${text.substring(0, 3000)}`;

    const systemPrompt = `You are a specialized enhancement AI focused on improving field extraction confidence to achieve 90% accuracy targets.

ENHANCEMENT MISSION:
- Maximize confidence scores for low-confidence fields
- Apply advanced extraction techniques
- Use alternative detection methods
- Provide detailed improvement explanations
- Achieve measurable accuracy gains

ENHANCEMENT ARSENAL:
1. Multi-pattern recognition systems
2. Context-aware field detection
3. Cross-field validation and inference
4. Fuzzy matching for partial matches
5. Business logic-based corrections
6. Alternative value suggestion

CONFIDENCE IMPROVEMENT TARGETS:
- Low confidence (<60%): Improve to 75%+
- Medium confidence (60-80%): Improve to 85%+
- Target confidence (80%+): Optimize to 90%+

Focus on achieving the highest possible accuracy improvements while maintaining reliability.`;

    return { prompt, systemPrompt };
  }

  /**
   * Get relevant fields for document type
   * @private
   */
  getRelevantFields(documentType) {
    const commonFields = [
      'kind', 'number', 'issue_date', 'seller_name', 'buyer_name',
      'total_net', 'total_gross', 'tax_value'
    ];

    const specificFields = DOCUMENT_KIND_FIELDS[documentType] || [];

    return [...commonFields, ...specificFields];
  }

  /**
   * Generate field descriptions for prompt
   * @private
   */
  generateFieldDescriptions(fields, language) {
    return fields.map(fieldName => {
      const fieldDef = ENHANCED_EXTRACTION_PATTERNS[fieldName];
      const importance = fieldDef?.importance || 'medium';
      const description = fieldDef?.description || `Field: ${fieldName}`;

      return `- ${fieldName} (${importance}): ${description}`;
    }).join('\n');
  }

  /**
   * Generate validation rules for critical fields
   * @private
   */
  generateValidationRules(criticalFields) {
    return criticalFields.map(field => {
      const fieldDef = ENHANCED_EXTRACTION_PATTERNS[field.name];
      let rules = [`${field.name}:`];

      if (fieldDef?.validation?.format) {
        rules.push(`  - Format: ${fieldDef.validation.format}`);
      }

      if (fieldDef?.patterns) {
        rules.push(`  - Patterns: ${fieldDef.patterns.length} detection patterns`);
      }

      rules.push(`  - Importance: ${fieldDef?.importance || 'medium'}`);

      return rules.join('\n');
    }).join('\n\n');
  }

  /**
   * Generate enhancement strategies for low confidence fields
   * @private
   */
  generateEnhancementStrategies(lowConfidenceFields) {
    return lowConfidenceFields.map(field => {
      const strategies = [];

      if (field.confidence < 0.4) {
        strategies.push('Complete re-extraction using alternative methods');
      } else if (field.confidence < 0.6) {
        strategies.push('Pattern-based enhancement and context analysis');
      } else {
        strategies.push('Fine-tuning and cross-validation');
      }

      return `${field.name}: ${strategies.join(', ')}`;
    }).join('\n');
  }

  /**
   * Generate document classification prompt
   * @param {string} text - Document text
   * @param {Object} options - Classification options
   * @returns {Object} - Prompt and system prompt
   */
  generateDocumentClassificationPrompt(text, options = {}) {
    const prompt = `Classify this document type with high accuracy.

CLASSIFICATION TARGETS:
- Document type (vat, proforma, bill, receipt, etc.)
- Document language
- Business context
- Processing complexity

RESPONSE FORMAT (JSON):
{
  "document_type": "detected_type",
  "confidence": 0.95,
  "language": "detected_language",
  "business_context": "description",
  "classification_reasoning": "detailed_explanation"
}

DOCUMENT TEXT:
${text.substring(0, 2000)}`;

    const systemPrompt = `You are a document classification expert with 95% accuracy requirements.

Focus on:
- Accurate document type detection
- Language identification
- Business context understanding
- High confidence classification

Use document structure, keywords, and formatting patterns for classification.`;

    return { prompt, systemPrompt };
  }

  /**
   * Clear template cache
   */
  clearCache() {
    this.templateCache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getCacheStats() {
    return {
      size: this.templateCache.size,
      keys: Array.from(this.templateCache.keys())
    };
  }
}
