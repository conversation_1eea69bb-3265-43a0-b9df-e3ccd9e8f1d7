/**
 * Field Validation Service
 * Implements business rule validation for extracted fields
 *
 * ASSIGNMENT-105: Enhanced DeepSeek Analysis Integration
 * Epic: EPIC-008 - Enhanced AI Processing (90% Accuracy Target)
 */

import { FIELD_VALIDATION_RULES } from '../../core/config/validationRules.js';
import { ENHANCED_EXTRACTION_PATTERNS, FIELDS_WITH_FIXED_VALUES } from '../../core/config/fieldDefinitions.js';
import { processingLogger } from '../../utils/ProcessingLogger.js';

export class FieldValidator {
  constructor() {
    this.validationHistory = new Map();
  }

  /**
   * Validate extracted fields using business rules
   * @param {Object} extractedFields - Fields extracted from document
   * @param {string} documentType - Type of document
   * @param {string} uploadId - Upload ID for tracking
   * @param {Object} options - Validation options
   * @returns {Object} - Validation results and corrected fields
   */
  validateFields(extractedFields, documentType, uploadId, options = {}) {
    try {
      processingLogger.debug('field_validation', 'Starting field validation', uploadId, {
        fieldsCount: Object.keys(extractedFields).length,
        documentType
      });

      const validationResults = {
        validatedFields: {},
        validationErrors: [],
        validationWarnings: [],
        correctedFields: {},
        validationMetadata: {
          timestamp: new Date().toISOString(),
          uploadId,
          documentType,
          totalFields: Object.keys(extractedFields).length,
          validationMethod: 'business_rules'
        }
      };

      // Validate each field
      for (const [fieldName, fieldData] of Object.entries(extractedFields)) {
        const fieldValidation = this.validateSingleField(
          fieldName, fieldData, documentType, uploadId, extractedFields
        );

        validationResults.validatedFields[fieldName] = fieldValidation;

        // Collect errors and warnings
        if (fieldValidation.errors.length > 0) {
          validationResults.validationErrors.push(...fieldValidation.errors);
        }
        if (fieldValidation.warnings.length > 0) {
          validationResults.validationWarnings.push(...fieldValidation.warnings);
        }

        // Track corrections
        if (fieldValidation.corrected) {
          validationResults.correctedFields[fieldName] = {
            original: fieldValidation.originalValue,
            corrected: fieldValidation.correctedValue,
            reason: fieldValidation.correctionReason
          };
        }
      }

      // Perform cross-field validation
      const crossValidation = this.performCrossFieldValidation(
        validationResults.validatedFields, documentType, uploadId
      );

      validationResults.crossValidationResults = crossValidation;
      validationResults.validationErrors.push(...crossValidation.errors);
      validationResults.validationWarnings.push(...crossValidation.warnings);

      // Calculate validation summary
      validationResults.validationMetadata.summary = this.calculateValidationSummary(
        validationResults
      );

      processingLogger.info('field_validation', 'Field validation completed', uploadId, {
        totalErrors: validationResults.validationErrors.length,
        totalWarnings: validationResults.validationWarnings.length,
        correctedFields: Object.keys(validationResults.correctedFields).length
      });

      // Store validation history
      this.validationHistory.set(uploadId, validationResults);

      return validationResults;

    } catch (error) {
      processingLogger.error('field_validation', 'Field validation failed', uploadId, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Validate a single field
   * @private
   */
  validateSingleField(fieldName, fieldData, documentType, uploadId, allFields) {
    const fieldValue = fieldData.value || fieldData;
    const fieldDefinition = ENHANCED_EXTRACTION_PATTERNS[fieldName];

    const validation = {
      fieldName,
      originalValue: fieldValue,
      correctedValue: fieldValue,
      isValid: true,
      corrected: false,
      correctionReason: null,
      errors: [],
      warnings: [],
      validationChecks: {}
    };

    try {
      // 1. Required field validation
      if (this.isRequiredField(fieldName, documentType)) {
        if (!fieldValue || fieldValue.toString().trim() === '') {
          validation.errors.push({
            type: 'required_field_missing',
            message: `Required field '${fieldName}' is missing or empty`,
            severity: 'error'
          });
          validation.isValid = false;
        }
        validation.validationChecks.required = true;
      }

      // 2. Format validation
      if (fieldDefinition && fieldDefinition.validation && fieldValue) {
        const formatValidation = this.validateFieldFormat(fieldName, fieldValue, fieldDefinition.validation);
        validation.validationChecks.format = formatValidation.isValid;

        if (!formatValidation.isValid) {
          validation.errors.push(...formatValidation.errors);
          validation.warnings.push(...formatValidation.warnings);
          validation.isValid = false;

          // Attempt automatic correction
          if (formatValidation.suggestedCorrection) {
            validation.correctedValue = formatValidation.suggestedCorrection;
            validation.corrected = true;
            validation.correctionReason = formatValidation.correctionReason;
          }
        }
      }

      // 3. Fixed values validation
      if (FIELDS_WITH_FIXED_VALUES[fieldName] && fieldValue) {
        const fixedValueValidation = this.validateFixedValue(fieldName, fieldValue);
        validation.validationChecks.fixedValue = fixedValueValidation.isValid;

        if (!fixedValueValidation.isValid) {
          validation.warnings.push({
            type: 'invalid_fixed_value',
            message: `Field '${fieldName}' has invalid value '${fieldValue}'. Expected one of: ${FIELDS_WITH_FIXED_VALUES[fieldName].join(', ')}`,
            severity: 'warning'
          });

          // Suggest closest match
          if (fixedValueValidation.suggestedValue) {
            validation.correctedValue = fixedValueValidation.suggestedValue;
            validation.corrected = true;
            validation.correctionReason = 'Corrected to closest valid option';
          }
        }
      }

      // 4. Data type validation
      const dataTypeValidation = this.validateDataType(fieldName, fieldValue);
      validation.validationChecks.dataType = dataTypeValidation.isValid;

      if (!dataTypeValidation.isValid) {
        validation.warnings.push(...dataTypeValidation.warnings);
      }

      // 5. Business logic validation
      const businessValidation = this.validateBusinessLogic(fieldName, fieldValue, allFields);
      validation.validationChecks.businessLogic = businessValidation.isValid;

      if (!businessValidation.isValid) {
        validation.errors.push(...businessValidation.errors);
        validation.warnings.push(...businessValidation.warnings);
        validation.isValid = false;
      }

    } catch (error) {
      validation.errors.push({
        type: 'validation_error',
        message: `Validation failed for field '${fieldName}': ${error.message}`,
        severity: 'error'
      });
      validation.isValid = false;
    }

    return validation;
  }

  /**
   * Check if field is required for document type
   * @private
   */
  isRequiredField(fieldName, documentType) {
    const requiredFields = FIELD_VALIDATION_RULES.required;

    // Check common required fields
    if (requiredFields.common && requiredFields.common.includes(fieldName)) {
      return true;
    }

    // Check document type specific required fields
    if (requiredFields[documentType] && requiredFields[documentType].includes(fieldName)) {
      return true;
    }

    return false;
  }

  /**
   * Validate field format
   * @private
   */
  validateFieldFormat(fieldName, fieldValue, validationRules) {
    const result = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestedCorrection: null,
      correctionReason: null
    };

    // Format regex validation
    if (validationRules.format) {
      try {
        const formatRegex = new RegExp(validationRules.format);
        if (!formatRegex.test(fieldValue)) {
          result.isValid = false;
          result.errors.push({
            type: 'format_validation_failed',
            message: `Field '${fieldName}' format is invalid. Value: '${fieldValue}'`,
            severity: 'error'
          });

          // Attempt format correction for common cases
          result.suggestedCorrection = this.attemptFormatCorrection(fieldName, fieldValue, validationRules);
          if (result.suggestedCorrection) {
            result.correctionReason = 'Automatic format correction applied';
          }
        }
      } catch (error) {
        result.warnings.push({
          type: 'invalid_validation_regex',
          message: `Invalid validation regex for field '${fieldName}': ${error.message}`,
          severity: 'warning'
        });
      }
    }

    // Length validation
    if (validationRules.minLength && fieldValue.length < validationRules.minLength) {
      result.isValid = false;
      result.errors.push({
        type: 'min_length_validation_failed',
        message: `Field '${fieldName}' is too short. Minimum length: ${validationRules.minLength}`,
        severity: 'error'
      });
    }

    if (validationRules.maxLength && fieldValue.length > validationRules.maxLength) {
      result.isValid = false;
      result.errors.push({
        type: 'max_length_validation_failed',
        message: `Field '${fieldName}' is too long. Maximum length: ${validationRules.maxLength}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate fixed value fields
   * @private
   */
  validateFixedValue(fieldName, fieldValue) {
    const validValues = FIELDS_WITH_FIXED_VALUES[fieldName];
    const result = {
      isValid: validValues.includes(fieldValue),
      suggestedValue: null
    };

    if (!result.isValid) {
      // Find closest match using simple string similarity
      result.suggestedValue = this.findClosestMatch(fieldValue, validValues);
    }

    return result;
  }

  /**
   * Validate data type
   * @private
   */
  validateDataType(fieldName, fieldValue) {
    const result = {
      isValid: true,
      warnings: []
    };

    // Date fields validation
    if (fieldName.includes('date') && fieldValue) {
      const dateValue = new Date(fieldValue);
      if (isNaN(dateValue.getTime())) {
        result.isValid = false;
        result.warnings.push({
          type: 'invalid_date_format',
          message: `Field '${fieldName}' contains invalid date: '${fieldValue}'`,
          severity: 'warning'
        });
      }
    }

    // Numeric fields validation
    if (fieldName.includes('amount') || fieldName.includes('total') || fieldName.includes('price')) {
      const numericValue = parseFloat(fieldValue);
      if (isNaN(numericValue)) {
        result.isValid = false;
        result.warnings.push({
          type: 'invalid_numeric_format',
          message: `Field '${fieldName}' should be numeric: '${fieldValue}'`,
          severity: 'warning'
        });
      }
    }

    return result;
  }

  /**
   * Validate business logic rules
   * @private
   */
  validateBusinessLogic(fieldName, fieldValue, allFields) {
    const result = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Date logic validation
    if (fieldName === 'due_date' && allFields.issue_date) {
      const issueDate = new Date(allFields.issue_date.value || allFields.issue_date);
      const dueDate = new Date(fieldValue);

      if (!isNaN(issueDate.getTime()) && !isNaN(dueDate.getTime())) {
        if (dueDate < issueDate) {
          result.isValid = false;
          result.errors.push({
            type: 'business_logic_violation',
            message: 'Due date cannot be before issue date',
            severity: 'error'
          });
        }
      }
    }

    // Amount calculation validation
    if (fieldName === 'total_gross' && allFields.total_net && allFields.tax_value) {
      const totalNet = parseFloat(allFields.total_net.value || allFields.total_net);
      const taxValue = parseFloat(allFields.tax_value.value || allFields.tax_value);
      const totalGross = parseFloat(fieldValue);

      if (!isNaN(totalNet) && !isNaN(taxValue) && !isNaN(totalGross)) {
        const calculatedGross = totalNet + taxValue;
        const difference = Math.abs(calculatedGross - totalGross);

        if (difference > 1.0) { // Allow small rounding differences
          result.warnings.push({
            type: 'calculation_mismatch',
            message: `Total gross (${totalGross}) doesn't match calculated value (${calculatedGross})`,
            severity: 'warning'
          });
        }
      }
    }

    return result;
  }

  /**
   * Perform cross-field validation
   * @private
   */
  performCrossFieldValidation(validatedFields, documentType, uploadId) {
    const result = {
      errors: [],
      warnings: [],
      validationChecks: {}
    };

    try {
      // Check for required field combinations
      const requiredCombinations = this.getRequiredFieldCombinations(documentType);

      for (const combination of requiredCombinations) {
        const missingFields = combination.filter(fieldName =>
          !validatedFields[fieldName] || !validatedFields[fieldName].originalValue
        );

        if (missingFields.length > 0) {
          result.errors.push({
            type: 'required_combination_missing',
            message: `Required field combination incomplete. Missing: ${missingFields.join(', ')}`,
            severity: 'error'
          });
        }
      }

      // Validate field relationships
      result.validationChecks.relationships = this.validateFieldRelationships(validatedFields);

    } catch (error) {
      result.errors.push({
        type: 'cross_validation_error',
        message: `Cross-field validation failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Get required field combinations for document type
   * @private
   */
  getRequiredFieldCombinations(documentType) {
    const combinations = {
      vat: [
        ['seller_name', 'seller_tax_id'],
        ['buyer_name', 'buyer_tax_id'],
        ['total_net', 'tax_value', 'total_gross']
      ],
      correction: [
        ['invoice_id', 'corrected_content_before', 'corrected_content_after']
      ]
    };

    return combinations[documentType] || [];
  }

  /**
   * Validate field relationships
   * @private
   */
  validateFieldRelationships(validatedFields) {
    // Implement relationship validation logic
    return { isValid: true, details: [] };
  }

  /**
   * Attempt format correction
   * @private
   */
  attemptFormatCorrection(fieldName, fieldValue, validationRules) {
    // Implement format correction logic for common cases
    if (fieldName === 'seller_tax_id' || fieldName === 'buyer_tax_id') {
      // Remove spaces and dashes from tax IDs
      return fieldValue.replace(/[\s\-]/g, '');
    }

    return null;
  }

  /**
   * Find closest match from valid values
   * @private
   */
  findClosestMatch(value, validValues) {
    let bestMatch = validValues[0];
    let bestScore = 0;

    for (const validValue of validValues) {
      const score = this.calculateStringSimilarity(value.toLowerCase(), validValue.toLowerCase());
      if (score > bestScore) {
        bestScore = score;
        bestMatch = validValue;
      }
    }

    return bestScore > 0.5 ? bestMatch : null;
  }

  /**
   * Calculate string similarity
   * @private
   */
  calculateStringSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.calculateEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate edit distance between strings
   * @private
   */
  calculateEditDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Calculate validation summary
   * @private
   */
  calculateValidationSummary(validationResults) {
    return {
      totalFields: validationResults.validationMetadata.totalFields,
      validFields: Object.values(validationResults.validatedFields).filter(f => f.isValid).length,
      invalidFields: Object.values(validationResults.validatedFields).filter(f => !f.isValid).length,
      correctedFields: Object.keys(validationResults.correctedFields).length,
      totalErrors: validationResults.validationErrors.length,
      totalWarnings: validationResults.validationWarnings.length,
      validationScore: this.calculateValidationScore(validationResults)
    };
  }

  /**
   * Calculate overall validation score
   * @private
   */
  calculateValidationScore(validationResults) {
    const totalFields = validationResults.validationMetadata.totalFields;
    if (totalFields === 0) return 1.0;

    const validFields = Object.values(validationResults.validatedFields).filter(f => f.isValid).length;
    return validFields / totalFields;
  }

  /**
   * Get validation history for debugging
   * @param {string} uploadId - Upload ID
   * @returns {Object|null} - Validation history or null if not found
   */
  getValidationHistory(uploadId) {
    return this.validationHistory.get(uploadId) || null;
  }

  /**
   * Clear validation history (for memory management)
   * @param {string} uploadId - Upload ID to clear, or null to clear all
   */
  clearValidationHistory(uploadId = null) {
    if (uploadId) {
      this.validationHistory.delete(uploadId);
    } else {
      this.validationHistory.clear();
    }
  }
}
