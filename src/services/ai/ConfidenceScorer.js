/**
 * Confidence Scoring Service
 * Implements advanced confidence scoring for field extraction accuracy
 *
 * ASSIGNMENT-105: Enhanced DeepSeek Analysis Integration
 * Epic: EPIC-008 - Enhanced AI Processing (90% Accuracy Target)
 */

import { ENHANCED_EXTRACTION_PATTERNS, FIELD_IMPORTANCE, CONFIDENCE_SCORING } from '../../core/config/fieldDefinitions.js';
import { processingLogger } from '../../utils/ProcessingLogger.js';

export class ConfidenceScorer {
  constructor() {
    this.scoringHistory = new Map();
  }

  /**
   * Calculate comprehensive confidence score for extracted fields
   * @param {Object} extractedFields - Fields extracted from document
   * @param {string} sourceText - Original document text
   * @param {string} uploadId - Upload ID for tracking
   * @param {Object} options - Scoring options
   * @returns {Object} - Confidence scores and analysis
   */
  calculateFieldConfidences(extractedFields, sourceText, uploadId, options = {}) {
    try {
      processingLogger.debug('confidence_scoring', 'Starting confidence calculation', uploadId, {
        fieldsCount: Object.keys(extractedFields).length,
        textLength: sourceText.length
      });

      const confidenceResults = {
        fieldScores: {},
        overallScore: 0,
        scoringMetadata: {
          timestamp: new Date().toISOString(),
          uploadId,
          totalFields: Object.keys(extractedFields).length,
          scoringMethod: 'comprehensive_multi_factor'
        }
      };

      let totalScore = 0;
      let scoredFields = 0;

      // Calculate confidence for each field
      for (const [fieldName, fieldData] of Object.entries(extractedFields)) {
        const fieldScore = this.calculateSingleFieldConfidence(
          fieldName, fieldData, sourceText, uploadId, options
        );

        confidenceResults.fieldScores[fieldName] = fieldScore;
        totalScore += fieldScore.finalScore;
        scoredFields++;
      }

      // Calculate overall confidence
      confidenceResults.overallScore = scoredFields > 0 ? totalScore / scoredFields : 0;

      // Add confidence distribution analysis
      confidenceResults.scoringMetadata.distribution = this.analyzeConfidenceDistribution(
        confidenceResults.fieldScores
      );

      processingLogger.info('confidence_scoring', 'Confidence calculation completed', uploadId, {
        overallScore: confidenceResults.overallScore,
        fieldsScored: scoredFields,
        highConfidenceFields: confidenceResults.scoringMetadata.distribution.high
      });

      // Store scoring history
      this.scoringHistory.set(uploadId, confidenceResults);

      return confidenceResults;

    } catch (error) {
      processingLogger.error('confidence_scoring', 'Confidence calculation failed', uploadId, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Calculate confidence score for a single field
   * @private
   */
  calculateSingleFieldConfidence(fieldName, fieldData, sourceText, uploadId, options) {
    try {
      const fieldDefinition = ENHANCED_EXTRACTION_PATTERNS[fieldName];
      const fieldValue = fieldData.value || fieldData;
      const baseConfidence = fieldData.confidence || 0;

      // Initialize scoring components
      const scoringComponents = {
        baseScore: baseConfidence,
        patternMatchScore: 0,
        formatValidationScore: 0,
        contextPositionScore: 0,
        crossValidationScore: 0,
        bonuses: 0
      };

      // 1. Pattern matching score (40% weight)
      if (fieldDefinition && fieldDefinition.patterns) {
        scoringComponents.patternMatchScore = this.calculatePatternMatchScore(
          fieldValue, fieldDefinition.patterns, sourceText
        );
      }

      // 2. Format validation score (25% weight)
      if (fieldDefinition && fieldDefinition.validation) {
        scoringComponents.formatValidationScore = this.calculateFormatValidationScore(
          fieldValue, fieldDefinition.validation
        );
      }

      // 3. Context position score (20% weight)
      scoringComponents.contextPositionScore = this.calculateContextPositionScore(
        fieldName, fieldValue, sourceText
      );

      // 4. Cross-validation score (15% weight)
      scoringComponents.crossValidationScore = this.calculateCrossValidationScore(
        fieldName, fieldValue, options.allFields || {}
      );

      // 5. Apply bonuses
      scoringComponents.bonuses = this.calculateBonuses(
        fieldName, fieldValue, fieldDefinition, scoringComponents
      );

      // Calculate weighted final score
      const weights = CONFIDENCE_SCORING.WEIGHTS;
      const finalScore = Math.min(1.0,
        (scoringComponents.patternMatchScore * weights.pattern_match) +
        (scoringComponents.formatValidationScore * weights.format_validation) +
        (scoringComponents.contextPositionScore * weights.context_position) +
        (scoringComponents.crossValidationScore * weights.cross_validation) +
        (scoringComponents.bonuses / 100) // Bonuses are percentage points
      );

      return {
        fieldName,
        fieldValue,
        finalScore: Math.max(0, finalScore),
        components: scoringComponents,
        fieldImportance: fieldDefinition?.importance || FIELD_IMPORTANCE.MEDIUM,
        meetsThreshold: this.meetsConfidenceThreshold(finalScore, fieldDefinition?.importance),
        scoringTimestamp: new Date().toISOString()
      };

    } catch (error) {
      processingLogger.error('confidence_scoring', `Field confidence calculation failed for ${fieldName}`, uploadId, {
        error: error.message
      });

      return {
        fieldName,
        fieldValue: fieldData.value || fieldData,
        finalScore: 0,
        error: error.message,
        scoringTimestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Calculate pattern matching score
   * @private
   */
  calculatePatternMatchScore(fieldValue, patterns, sourceText) {
    if (!patterns || patterns.length === 0) return 0.5; // Default score if no patterns

    let bestScore = 0;
    let matchCount = 0;

    for (const pattern of patterns) {
      try {
        const regex = new RegExp(pattern, 'gi');
        const matches = sourceText.match(regex);

        if (matches) {
          matchCount++;
          // Check if the field value matches any of the found patterns
          const valueMatches = matches.some(match =>
            match.toLowerCase().includes(fieldValue.toLowerCase()) ||
            fieldValue.toLowerCase().includes(match.toLowerCase())
          );

          if (valueMatches) {
            bestScore = Math.max(bestScore, 0.9);
          } else {
            bestScore = Math.max(bestScore, 0.6);
          }
        }
      } catch (error) {
        // Skip invalid regex patterns
        continue;
      }
    }

    // Bonus for multiple pattern matches
    if (matchCount > 1) {
      bestScore = Math.min(1.0, bestScore + 0.1);
    }

    return bestScore;
  }

  /**
   * Calculate format validation score
   * @private
   */
  calculateFormatValidationScore(fieldValue, validation) {
    if (!validation) return 0.7; // Default score if no validation rules

    let score = 0;

    // Format validation
    if (validation.format) {
      try {
        const formatRegex = new RegExp(validation.format);
        if (formatRegex.test(fieldValue)) {
          score += 0.8;
        } else {
          score += 0.3; // Partial score for having a value
        }
      } catch (error) {
        score += 0.5; // Default if regex is invalid
      }
    }

    // Length validation
    if (validation.minLength && fieldValue.length >= validation.minLength) {
      score += 0.1;
    }
    if (validation.maxLength && fieldValue.length <= validation.maxLength) {
      score += 0.1;
    }

    return Math.min(1.0, score);
  }

  /**
   * Calculate context position score
   * @private
   */
  calculateContextPositionScore(fieldName, fieldValue, sourceText) {
    // Look for field name or related keywords near the extracted value
    const contextKeywords = this.getContextKeywords(fieldName);
    const lowerText = sourceText.toLowerCase();
    const lowerValue = fieldValue.toLowerCase();

    let score = 0.5; // Base score

    // Find the position of the field value in the text
    const valueIndex = lowerText.indexOf(lowerValue);
    if (valueIndex === -1) return 0.3; // Value not found in text

    // Check for context keywords within 100 characters before and after
    const contextStart = Math.max(0, valueIndex - 100);
    const contextEnd = Math.min(lowerText.length, valueIndex + fieldValue.length + 100);
    const contextText = lowerText.substring(contextStart, contextEnd);

    let keywordMatches = 0;
    for (const keyword of contextKeywords) {
      if (contextText.includes(keyword.toLowerCase())) {
        keywordMatches++;
        score += 0.1;
      }
    }

    // Bonus for multiple keyword matches
    if (keywordMatches > 1) {
      score += 0.1;
    }

    return Math.min(1.0, score);
  }

  /**
   * Calculate cross-validation score
   * @private
   */
  calculateCrossValidationScore(fieldName, fieldValue, allFields) {
    // Implement business logic validation between related fields
    let score = 0.5; // Base score

    // Example: Validate date relationships
    if (fieldName === 'due_date' && allFields.issue_date) {
      const issueDate = new Date(allFields.issue_date.value || allFields.issue_date);
      const dueDate = new Date(fieldValue);

      if (!isNaN(issueDate.getTime()) && !isNaN(dueDate.getTime())) {
        if (dueDate >= issueDate) {
          score += 0.3; // Due date should be after issue date
        }
      }
    }

    // Example: Validate amount calculations
    if (fieldName === 'total_gross' && allFields.total_net && allFields.tax_value) {
      const totalNet = parseFloat(allFields.total_net.value || allFields.total_net);
      const taxValue = parseFloat(allFields.tax_value.value || allFields.tax_value);
      const totalGross = parseFloat(fieldValue);

      if (!isNaN(totalNet) && !isNaN(taxValue) && !isNaN(totalGross)) {
        const calculatedGross = totalNet + taxValue;
        const difference = Math.abs(calculatedGross - totalGross);

        if (difference < 0.01) {
          score += 0.4; // Perfect calculation match
        } else if (difference < 1.0) {
          score += 0.2; // Close calculation match
        }
      }
    }

    return Math.min(1.0, score);
  }

  /**
   * Calculate bonus points
   * @private
   */
  calculateBonuses(fieldName, fieldValue, fieldDefinition, scoringComponents) {
    let bonuses = 0;

    // Multiple pattern match bonus
    if (scoringComponents.patternMatchScore > 0.8) {
      bonuses += CONFIDENCE_SCORING.BONUSES.multiple_pattern_match;
    }

    // Perfect format match bonus
    if (scoringComponents.formatValidationScore >= 0.9) {
      bonuses += CONFIDENCE_SCORING.BONUSES.format_perfect_match;
    }

    // High context relevance bonus
    if (scoringComponents.contextPositionScore >= 0.8) {
      bonuses += CONFIDENCE_SCORING.BONUSES.context_high_relevance;
    }

    return bonuses;
  }

  /**
   * Check if field meets confidence threshold
   * @private
   */
  meetsConfidenceThreshold(score, fieldImportance) {
    const threshold = CONFIDENCE_SCORING.THRESHOLDS[fieldImportance] || 75;
    return (score * 100) >= threshold;
  }

  /**
   * Get context keywords for field
   * @private
   */
  getContextKeywords(fieldName) {
    const keywordMap = {
      seller_name: ['seller', 'vendor', 'from', 'sprzedawca', 'dostawca'],
      buyer_name: ['buyer', 'customer', 'to', 'nabywca', 'klient'],
      total_gross: ['total', 'sum', 'razem', 'suma', 'brutto'],
      total_net: ['net', 'netto', 'subtotal'],
      tax_value: ['tax', 'vat', 'podatek'],
      issue_date: ['date', 'issued', 'data', 'wystawiono'],
      due_date: ['due', 'payment', 'termin', 'płatność'],
      number: ['number', 'nr', 'numer', 'invoice']
    };

    return keywordMap[fieldName] || [fieldName];
  }

  /**
   * Analyze confidence distribution
   * @private
   */
  analyzeConfidenceDistribution(fieldScores) {
    const scores = Object.values(fieldScores).map(score => score.finalScore);

    return {
      high: scores.filter(s => s >= 0.8).length,
      medium: scores.filter(s => s >= 0.6 && s < 0.8).length,
      low: scores.filter(s => s < 0.6).length,
      average: scores.reduce((sum, s) => sum + s, 0) / scores.length,
      min: Math.min(...scores),
      max: Math.max(...scores)
    };
  }

  /**
   * Get scoring history for debugging
   * @param {string} uploadId - Upload ID
   * @returns {Object|null} - Scoring history or null if not found
   */
  getScoringHistory(uploadId) {
    return this.scoringHistory.get(uploadId) || null;
  }

  /**
   * Clear scoring history (for memory management)
   * @param {string} uploadId - Upload ID to clear, or null to clear all
   */
  clearScoringHistory(uploadId = null) {
    if (uploadId) {
      this.scoringHistory.delete(uploadId);
    } else {
      this.scoringHistory.clear();
    }
  }
}
