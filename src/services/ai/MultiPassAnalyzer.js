/**
 * Multi-Pass Analysis Service
 * Implements multi-pass DeepSeek analysis for 90% accuracy target
 *
 * ASSIGNMENT-105: Enhanced DeepSeek Analysis Integration
 * Epic: EPIC-008 - Enhanced AI Processing (90% Accuracy Target)
 */

import { DeepSeekAPI } from '../../api/DeepSeekAPI.js';
import { processingLogger } from '../../utils/ProcessingLogger.js';
import { ENHANCED_EXTRACTION_PATTERNS, FIELD_IMPORTANCE, CONFIDENCE_SCORING } from '../../core/config/fieldDefinitions.js';

/**
 * Multi-pass analysis configuration
 */
const ANALYSIS_PASSES = {
  BASIC_EXTRACTION: {
    name: 'Basic Field Extraction',
    temperature: 0.1,
    max_tokens: 4096,
    focus: 'basic_fields'
  },
  CRITICAL_VALIDATION: {
    name: 'Critical Field Validation',
    temperature: 0.05,
    max_tokens: 2048,
    focus: 'critical_fields'
  },
  CONFIDENCE_ENHANCEMENT: {
    name: 'Confidence Enhancement',
    temperature: 0.15,
    max_tokens: 3072,
    focus: 'low_confidence_fields'
  }
};

export class MultiPassAnalyzer {
  constructor() {
    this.deepSeekAPI = new DeepSeekAPI();
    this.analysisHistory = new Map();
  }

  /**
   * Perform multi-pass analysis on document text
   * @param {string} text - Document text to analyze
   * @param {string} apiKey - DeepSeek API key
   * @param {string} uploadId - Upload ID for tracking
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} - Multi-pass analysis results
   */
  async performMultiPassAnalysis(text, apiKey, uploadId, options = {}) {
    try {
      const startTime = performance.now();

      processingLogger.info('multi_pass_analysis', 'Starting multi-pass analysis', uploadId, {
        textLength: text.length,
        passes: Object.keys(ANALYSIS_PASSES).length
      });

      // Initialize analysis context
      const analysisContext = {
        text,
        uploadId,
        options,
        passes: {},
        finalResults: {},
        confidenceScores: {},
        metadata: {
          startTime,
          totalPasses: Object.keys(ANALYSIS_PASSES).length
        }
      };

      // Pass 1: Basic Field Extraction
      analysisContext.passes.basic = await this.performBasicExtraction(
        text, apiKey, uploadId, options
      );

      // Pass 2: Critical Field Validation
      analysisContext.passes.critical = await this.performCriticalValidation(
        text, apiKey, uploadId, analysisContext.passes.basic, options
      );

      // Pass 3: Confidence Enhancement
      analysisContext.passes.enhancement = await this.performConfidenceEnhancement(
        text, apiKey, uploadId, analysisContext.passes, options
      );

      // Consolidate results from all passes
      const consolidatedResults = await this.consolidateResults(analysisContext);

      const totalTime = performance.now() - startTime;

      processingLogger.info('multi_pass_analysis', 'Multi-pass analysis completed', uploadId, {
        totalTimeMs: totalTime,
        passesCompleted: Object.keys(analysisContext.passes).length,
        fieldsExtracted: Object.keys(consolidatedResults.fields).length,
        averageConfidence: consolidatedResults.metadata.averageConfidence
      });

      // Store analysis history for debugging
      this.analysisHistory.set(uploadId, analysisContext);

      return consolidatedResults;

    } catch (error) {
      processingLogger.error('multi_pass_analysis', 'Multi-pass analysis failed', uploadId, {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Perform basic field extraction (Pass 1)
   * @private
   */
  async performBasicExtraction(text, apiKey, uploadId, options) {
    try {
      const passConfig = ANALYSIS_PASSES.BASIC_EXTRACTION;

      processingLogger.debug('multi_pass_analysis', 'Starting basic extraction pass', uploadId);

      const prompt = this.generateBasicExtractionPrompt(text, options);

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        temperature: passConfig.temperature,
        max_tokens: passConfig.max_tokens,
        response_format: { type: 'json_object' },
        systemPrompt: this.getBasicExtractionSystemPrompt()
      }, uploadId);

      if (!response.success) {
        throw new Error(`Basic extraction failed: ${response.error}`);
      }

      const extractedData = this.deepSeekAPI.extractJSON(response.content);

      processingLogger.debug('multi_pass_analysis', 'Basic extraction completed', uploadId, {
        fieldsExtracted: Object.keys(extractedData.fields || {}).length,
        hasMetadata: !!extractedData.metadata
      });

      return {
        success: true,
        fields: extractedData.fields || {},
        metadata: extractedData.metadata || {},
        passInfo: {
          name: passConfig.name,
          timestamp: new Date().toISOString(),
          apiUsage: response.usage
        }
      };

    } catch (error) {
      processingLogger.error('multi_pass_analysis', 'Basic extraction failed', uploadId, {
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        fields: {},
        metadata: {}
      };
    }
  }

  /**
   * Perform critical field validation (Pass 2)
   * @private
   */
  async performCriticalValidation(text, apiKey, uploadId, basicResults, options) {
    try {
      const passConfig = ANALYSIS_PASSES.CRITICAL_VALIDATION;

      processingLogger.debug('multi_pass_analysis', 'Starting critical validation pass', uploadId);

      // Identify critical fields that need validation
      const criticalFields = this.identifyCriticalFields(basicResults.fields);

      if (criticalFields.length === 0) {
        processingLogger.debug('multi_pass_analysis', 'No critical fields to validate', uploadId);
        return { success: true, fields: {}, validations: {} };
      }

      const prompt = this.generateCriticalValidationPrompt(text, criticalFields, options);

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        temperature: passConfig.temperature,
        max_tokens: passConfig.max_tokens,
        response_format: { type: 'json_object' },
        systemPrompt: this.getCriticalValidationSystemPrompt()
      }, uploadId);

      if (!response.success) {
        throw new Error(`Critical validation failed: ${response.error}`);
      }

      const validationData = this.deepSeekAPI.extractJSON(response.content);

      processingLogger.debug('multi_pass_analysis', 'Critical validation completed', uploadId, {
        fieldsValidated: criticalFields.length,
        validationsPerformed: Object.keys(validationData.validations || {}).length
      });

      return {
        success: true,
        fields: validationData.fields || {},
        validations: validationData.validations || {},
        passInfo: {
          name: passConfig.name,
          timestamp: new Date().toISOString(),
          apiUsage: response.usage,
          criticalFieldsCount: criticalFields.length
        }
      };

    } catch (error) {
      processingLogger.error('multi_pass_analysis', 'Critical validation failed', uploadId, {
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        fields: {},
        validations: {}
      };
    }
  }

  /**
   * Identify critical fields that need validation
   * @private
   */
  identifyCriticalFields(fields) {
    const criticalFields = [];

    for (const [fieldName, fieldData] of Object.entries(fields)) {
      const fieldDef = ENHANCED_EXTRACTION_PATTERNS[fieldName];
      if (fieldDef && fieldDef.importance === FIELD_IMPORTANCE.CRITICAL) {
        criticalFields.push({
          name: fieldName,
          value: fieldData.value || fieldData,
          confidence: fieldData.confidence || 0,
          definition: fieldDef
        });
      }
    }

    return criticalFields;
  }

  /**
   * Generate basic extraction prompt
   * @private
   */
  generateBasicExtractionPrompt(text, options) {
    const documentType = options.documentType || 'invoice';
    const language = options.language || 'pl';

    return `Extract all relevant fields from this ${documentType} document. Focus on accuracy and completeness.

Document Language: ${language}
Document Type: ${documentType}

Return JSON with this structure:
{
  "fields": {
    "field_name": {
      "value": "extracted_value",
      "confidence": 0.85,
      "source_text": "text_where_found"
    }
  },
  "metadata": {
    "document_type": "detected_type",
    "language": "detected_language",
    "extraction_method": "basic_pass"
  }
}

Document text:
${text.substring(0, 3000)}`;
  }

  /**
   * Get basic extraction system prompt
   * @private
   */
  getBasicExtractionSystemPrompt() {
    return `You are an expert document analysis AI specializing in extracting structured data from business documents.

Your task is to perform the first pass of a multi-pass analysis system. Focus on:
1. Extracting all visible fields with high confidence
2. Providing confidence scores (0.0-1.0) for each field
3. Including source text references for verification
4. Detecting document type and language accurately

Be thorough but conservative in your confidence scoring. This is the foundation pass for subsequent validation.`;
  }

  /**
   * Generate critical validation prompt
   * @private
   */
  generateCriticalValidationPrompt(text, criticalFields, options) {
    const fieldsToValidate = criticalFields.map(field =>
      `${field.name}: "${field.value}" (confidence: ${field.confidence})`
    ).join('\n');

    return `Validate these critical fields extracted from the document. Re-examine the text carefully and provide corrected values if needed.

Critical fields to validate:
${fieldsToValidate}

Return JSON with this structure:
{
  "fields": {
    "field_name": {
      "value": "validated_value",
      "confidence": 0.95,
      "validation_status": "confirmed|corrected|uncertain",
      "correction_reason": "reason_if_corrected"
    }
  },
  "validations": {
    "field_name": {
      "original_value": "original",
      "validated_value": "validated",
      "confidence_change": 0.1,
      "validation_notes": "detailed_notes"
    }
  }
}

Document text:
${text.substring(0, 3000)}`;
  }

  /**
   * Get critical validation system prompt
   * @private
   */
  getCriticalValidationSystemPrompt() {
    return `You are a specialized validation AI focused on critical field accuracy.

Your task is to perform the second pass validation of critical fields. Focus on:
1. Re-examining critical fields with extreme care
2. Correcting any errors found in the basic extraction
3. Increasing confidence scores for validated fields
4. Providing detailed validation notes for any changes

Be meticulous and precise. Critical fields must be 90%+ accurate.`;
  }

  /**
   * Perform confidence enhancement (Pass 3)
   * @private
   */
  async performConfidenceEnhancement(text, apiKey, uploadId, previousPasses, options) {
    try {
      const passConfig = ANALYSIS_PASSES.CONFIDENCE_ENHANCEMENT;

      processingLogger.debug('multi_pass_analysis', 'Starting confidence enhancement pass', uploadId);

      // Identify low confidence fields from previous passes
      const lowConfidenceFields = this.identifyLowConfidenceFields(previousPasses);

      if (lowConfidenceFields.length === 0) {
        processingLogger.debug('multi_pass_analysis', 'No low confidence fields to enhance', uploadId);
        return { success: true, fields: {}, enhancements: {} };
      }

      const prompt = this.generateConfidenceEnhancementPrompt(text, lowConfidenceFields, options);

      const response = await this.deepSeekAPI.callAPI(prompt, apiKey, {
        temperature: passConfig.temperature,
        max_tokens: passConfig.max_tokens,
        response_format: { type: 'json_object' },
        systemPrompt: this.getConfidenceEnhancementSystemPrompt()
      }, uploadId);

      if (!response.success) {
        throw new Error(`Confidence enhancement failed: ${response.error}`);
      }

      const enhancementData = this.deepSeekAPI.extractJSON(response.content);

      processingLogger.debug('multi_pass_analysis', 'Confidence enhancement completed', uploadId, {
        fieldsEnhanced: lowConfidenceFields.length,
        enhancementsApplied: Object.keys(enhancementData.enhancements || {}).length
      });

      return {
        success: true,
        fields: enhancementData.fields || {},
        enhancements: enhancementData.enhancements || {},
        passInfo: {
          name: passConfig.name,
          timestamp: new Date().toISOString(),
          apiUsage: response.usage,
          lowConfidenceFieldsCount: lowConfidenceFields.length
        }
      };

    } catch (error) {
      processingLogger.error('multi_pass_analysis', 'Confidence enhancement failed', uploadId, {
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        fields: {},
        enhancements: {}
      };
    }
  }

  /**
   * Identify low confidence fields from previous passes
   * @private
   */
  identifyLowConfidenceFields(previousPasses) {
    const lowConfidenceFields = [];
    const confidenceThreshold = 0.8; // 80% threshold for enhancement

    // Check basic extraction results
    if (previousPasses.basic && previousPasses.basic.fields) {
      for (const [fieldName, fieldData] of Object.entries(previousPasses.basic.fields)) {
        const confidence = fieldData.confidence || 0;
        if (confidence < confidenceThreshold) {
          lowConfidenceFields.push({
            name: fieldName,
            value: fieldData.value || fieldData,
            confidence: confidence,
            source: 'basic_extraction',
            needsEnhancement: true
          });
        }
      }
    }

    return lowConfidenceFields;
  }

  /**
   * Generate confidence enhancement prompt
   * @private
   */
  generateConfidenceEnhancementPrompt(text, lowConfidenceFields, options) {
    const fieldsToEnhance = lowConfidenceFields.map(field =>
      `${field.name}: "${field.value}" (confidence: ${field.confidence})`
    ).join('\n');

    return `Re-examine these low confidence fields and try to improve their accuracy and confidence scores. Look for alternative patterns, context clues, and cross-references.

Low confidence fields to enhance:
${fieldsToEnhance}

Return JSON with this structure:
{
  "fields": {
    "field_name": {
      "value": "enhanced_value",
      "confidence": 0.90,
      "enhancement_method": "pattern_matching|context_analysis|cross_reference",
      "improvement_notes": "explanation_of_improvement"
    }
  },
  "enhancements": {
    "field_name": {
      "original_confidence": 0.65,
      "enhanced_confidence": 0.90,
      "confidence_gain": 0.25,
      "enhancement_reason": "detailed_explanation"
    }
  }
}

Document text:
${text.substring(0, 3000)}`;
  }

  /**
   * Get confidence enhancement system prompt
   * @private
   */
  getConfidenceEnhancementSystemPrompt() {
    return `You are a specialized enhancement AI focused on improving field extraction confidence.

Your task is to perform the third pass enhancement of low confidence fields. Focus on:
1. Re-analyzing fields with low confidence scores
2. Using alternative extraction methods and patterns
3. Cross-referencing with other document elements
4. Providing detailed explanations for improvements

Your goal is to achieve 90%+ confidence for as many fields as possible.`;
  }

  /**
   * Consolidate results from all analysis passes
   * @private
   */
  async consolidateResults(analysisContext) {
    try {
      const { passes, uploadId } = analysisContext;

      processingLogger.debug('multi_pass_analysis', 'Consolidating results from all passes', uploadId);

      const consolidatedFields = {};
      const consolidationMetadata = {
        passesUsed: [],
        fieldSources: {},
        confidenceDistribution: {},
        averageConfidence: 0,
        totalFields: 0
      };

      // Consolidate fields from all successful passes
      for (const [passName, passResult] of Object.entries(passes)) {
        if (passResult.success && passResult.fields) {
          consolidationMetadata.passesUsed.push(passName);

          for (const [fieldName, fieldData] of Object.entries(passResult.fields)) {
            // Use the highest confidence version of each field
            const currentField = consolidatedFields[fieldName];
            const newConfidence = fieldData.confidence || 0;

            if (!currentField || newConfidence > (currentField.confidence || 0)) {
              consolidatedFields[fieldName] = {
                ...fieldData,
                source_pass: passName,
                consolidation_timestamp: new Date().toISOString()
              };
              consolidationMetadata.fieldSources[fieldName] = passName;
            }
          }
        }
      }

      // Calculate confidence statistics
      const confidences = Object.values(consolidatedFields)
        .map(field => field.confidence || 0)
        .filter(conf => conf > 0);

      if (confidences.length > 0) {
        consolidationMetadata.averageConfidence =
          confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;

        // Confidence distribution
        consolidationMetadata.confidenceDistribution = {
          high: confidences.filter(c => c >= 0.8).length,
          medium: confidences.filter(c => c >= 0.6 && c < 0.8).length,
          low: confidences.filter(c => c < 0.6).length
        };
      }

      consolidationMetadata.totalFields = Object.keys(consolidatedFields).length;

      processingLogger.info('multi_pass_analysis', 'Results consolidation completed', uploadId, {
        totalFields: consolidationMetadata.totalFields,
        averageConfidence: consolidationMetadata.averageConfidence,
        passesUsed: consolidationMetadata.passesUsed.length
      });

      return {
        success: true,
        fields: consolidatedFields,
        metadata: consolidationMetadata,
        analysisHistory: {
          passes: Object.keys(passes).map(passName => ({
            name: passName,
            success: passes[passName].success,
            fieldsExtracted: Object.keys(passes[passName].fields || {}).length,
            timestamp: passes[passName].passInfo?.timestamp
          }))
        }
      };

    } catch (error) {
      processingLogger.error('multi_pass_analysis', 'Results consolidation failed', analysisContext.uploadId, {
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Get analysis history for debugging
   * @param {string} uploadId - Upload ID
   * @returns {Object|null} - Analysis history or null if not found
   */
  getAnalysisHistory(uploadId) {
    return this.analysisHistory.get(uploadId) || null;
  }

  /**
   * Clear analysis history (for memory management)
   * @param {string} uploadId - Upload ID to clear, or null to clear all
   */
  clearAnalysisHistory(uploadId = null) {
    if (uploadId) {
      this.analysisHistory.delete(uploadId);
    } else {
      this.analysisHistory.clear();
    }
  }
}
