/**
 * PipelineDataStorageService - Comprehensive data storage for pipeline results
 * Stores all intermediate results from multi-step document processing pipeline
 */

import { processingLogger } from '../utils/ProcessingLogger.js';

export class PipelineDataStorageService {
  constructor() {
    this.storageKey = 'mvat_pipeline_data';
    this.maxStoredDocuments = 100; // Limit storage to prevent memory issues
  }

  /**
   * Store complete pipeline results for a document
   * @param {string} documentId - Unique document identifier
   * @param {Object} documentData - Complete document processing data
   * @returns {Promise<void>}
   */
  async storeDocumentData(documentId, documentData) {
    try {
      const storageData = await this.getStorageData();

      // Create comprehensive document record
      const documentRecord = {
        documentId,
        fileName: documentData.fileName,
        fileSize: documentData.fileSize,
        fileType: documentData.fileType,
        timestamp: new Date().toISOString(),
        processingStartTime: documentData.processingStartTime,
        processingEndTime: documentData.processingEndTime,
        totalProcessingTime: documentData.totalProcessingTime,
        steps: {
          pdf_extraction: this.formatStepData(documentData.steps?.pdf_extraction),
          deepseek_analysis: this.formatStepData(documentData.steps?.deepseek_analysis),
          tesseract_reference: this.formatStepData(documentData.steps?.tesseract_reference),
          field_mapping: this.formatStepData(documentData.steps?.field_mapping),
          data_validation: this.formatStepData(documentData.steps?.data_validation),
          final_output: this.formatStepData(documentData.steps?.final_output)
        },
        finalResult: {
          success: documentData.finalResult?.success || false,
          accuracy: documentData.finalResult?.accuracy || 0,
          extractedFields: documentData.finalResult?.extractedFields || {},
          errors: documentData.finalResult?.errors || [],
          warnings: documentData.finalResult?.warnings || []
        },
        metadata: {
          language: documentData.metadata?.language || 'unknown',
          documentType: documentData.metadata?.documentType || 'unknown',
          confidence: documentData.metadata?.confidence || 0,
          processingVersion: '1.5.0'
        }
      };

      // Add to storage with size limit
      storageData.documents[documentId] = documentRecord;

      // Enforce storage limit
      await this.enforceStorageLimit(storageData);

      // Save to storage
      await this.saveStorageData(storageData);

      console.log(`✅ Pipeline data stored for document: ${documentId}`);
    } catch (error) {
      console.error('❌ Failed to store pipeline data:', error);
      throw error;
    }
  }

  /**
   * Store individual step result
   * @param {string} documentId - Document identifier
   * @param {string} stepId - Step identifier
   * @param {Object} stepResult - Step result data
   * @returns {Promise<void>}
   */
  async storeStepResult(documentId, stepId, stepResult) {
    try {
      const storageData = await this.getStorageData();

      if (!storageData.documents[documentId]) {
        storageData.documents[documentId] = {
          documentId,
          timestamp: new Date().toISOString(),
          steps: {}
        };
      }

      storageData.documents[documentId].steps[stepId] = this.formatStepData(stepResult);
      storageData.documents[documentId].lastUpdated = new Date().toISOString();

      await this.saveStorageData(storageData);

      console.log(`✅ Step ${stepId} data stored for document: ${documentId}`);
    } catch (error) {
      console.error(`❌ Failed to store step ${stepId} data:`, error);
      throw error;
    }
  }

  /**
   * Retrieve document data by ID
   * @param {string} documentId - Document identifier
   * @returns {Promise<Object|null>} - Document data or null if not found
   */
  async getDocumentData(documentId) {
    try {
      const storageData = await this.getStorageData();
      return storageData.documents[documentId] || null;
    } catch (error) {
      console.error('❌ Failed to retrieve document data:', error);
      return null;
    }
  }

  /**
   * Retrieve all stored documents
   * @returns {Promise<Array>} - Array of document records
   */
  async getAllDocuments() {
    try {
      const storageData = await this.getStorageData();
      return Object.values(storageData.documents).sort((a, b) =>
        new Date(b.timestamp) - new Date(a.timestamp)
      );
    } catch (error) {
      console.error('❌ Failed to retrieve all documents:', error);
      return [];
    }
  }

  /**
   * Get storage statistics
   * @returns {Promise<Object>} - Storage statistics
   */
  async getStorageStats() {
    try {
      const storageData = await this.getStorageData();
      const documents = Object.values(storageData.documents);

      return {
        totalDocuments: documents.length,
        totalSize: JSON.stringify(storageData).length,
        oldestDocument: documents.length > 0 ?
          Math.min(...documents.map(d => new Date(d.timestamp).getTime())) : null,
        newestDocument: documents.length > 0 ?
          Math.max(...documents.map(d => new Date(d.timestamp).getTime())) : null,
        averageAccuracy: documents.length > 0 ?
          documents.reduce((sum, d) => sum + (d.finalResult?.accuracy || 0), 0) / documents.length : 0,
        successRate: documents.length > 0 ?
          (documents.filter(d => d.finalResult?.success).length / documents.length) * 100 : 0
      };
    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return {
        totalDocuments: 0,
        totalSize: 0,
        oldestDocument: null,
        newestDocument: null,
        averageAccuracy: 0,
        successRate: 0
      };
    }
  }

  /**
   * Clear all stored data
   * @returns {Promise<void>}
   */
  async clearAllData() {
    try {
      const emptyData = {
        version: '1.5.0',
        created: new Date().toISOString(),
        documents: {}
      };

      await this.saveStorageData(emptyData);
      console.log('✅ All pipeline data cleared');
    } catch (error) {
      console.error('❌ Failed to clear pipeline data:', error);
      throw error;
    }
  }

  /**
   * Format step data for storage
   * @param {Object} stepData - Raw step data
   * @returns {Object} - Formatted step data
   */
  formatStepData(stepData) {
    if (!stepData) { return null; }

    return {
      success: stepData.success || false,
      timing: stepData.timing || 0,
      timestamp: new Date().toISOString(),
      data: stepData.data || stepData,
      error: stepData.error || null,
      confidence: stepData.confidence || 0,
      method: stepData.method || 'unknown'
    };
  }

  /**
   * Get storage data from Chrome storage or localStorage
   * @returns {Promise<Object>} - Storage data
   */
  async getStorageData() {
    try {
      let storageData;

      if (chrome?.storage?.local) {
        const result = await chrome.storage.local.get(this.storageKey);
        storageData = result[this.storageKey];
      } else {
        const stored = localStorage.getItem(this.storageKey);
        storageData = stored ? JSON.parse(stored) : null;
      }

      // Initialize if empty
      if (!storageData) {
        storageData = {
          version: '1.5.0',
          created: new Date().toISOString(),
          documents: {}
        };
      }

      return storageData;
    } catch (error) {
      console.error('❌ Failed to get storage data:', error);
      return {
        version: '1.5.0',
        created: new Date().toISOString(),
        documents: {}
      };
    }
  }

  /**
   * Save storage data to Chrome storage or localStorage
   * @param {Object} storageData - Data to save
   * @returns {Promise<void>}
   */
  async saveStorageData(storageData) {
    try {
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ [this.storageKey]: storageData });
      } else {
        localStorage.setItem(this.storageKey, JSON.stringify(storageData));
      }
    } catch (error) {
      console.error('❌ Failed to save storage data:', error);
      throw error;
    }
  }

  /**
   * Enforce storage limit by removing oldest documents
   * @param {Object} storageData - Storage data to limit
   * @returns {Promise<void>}
   */
  async enforceStorageLimit(storageData) {
    const documents = Object.values(storageData.documents);

    if (documents.length > this.maxStoredDocuments) {
      // Sort by timestamp and remove oldest
      const sortedDocs = documents.sort((a, b) =>
        new Date(a.timestamp) - new Date(b.timestamp)
      );

      const toRemove = sortedDocs.slice(0, documents.length - this.maxStoredDocuments);

      for (const doc of toRemove) {
        delete storageData.documents[doc.documentId];
      }

      console.log(`🧹 Removed ${toRemove.length} old documents to enforce storage limit`);
    }
  }
}

// Export singleton instance
export const pipelineDataStorage = new PipelineDataStorageService();
