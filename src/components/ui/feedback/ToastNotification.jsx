/**
 * Toast Notification System for Pipeline Window
 * Provides real-time feedback for pipeline operations
 */

import React, { useState, useEffect, useCallback } from 'react';
import { X, CheckCircle, AlertCircle, XCircle, Info, Clock } from 'lucide-react';

// Toast types
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading'
};

// Toast positions
export const TOAST_POSITIONS = {
  TOP_RIGHT: 'top-right',
  TOP_LEFT: 'top-left',
  BOTTOM_RIGHT: 'bottom-right',
  BOTTOM_LEFT: 'bottom-left',
  TOP_CENTER: 'top-center',
  BOTTOM_CENTER: 'bottom-center'
};

/**
 * Individual Toast Component
 */
const Toast = ({
  id,
  type = TOAST_TYPES.INFO,
  title,
  message,
  duration = 5000,
  onClose,
  actions = [],
  progress = null
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (duration > 0 && type !== TOAST_TYPES.LOADING) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, type]);

  const handleClose = useCallback(() => {
    setIsExiting(true);
    setTimeout(() => {
      onClose?.(id);
    }, 300);
  }, [id, onClose]);

  const getToastConfig = () => {
    switch (type) {
      case TOAST_TYPES.SUCCESS:
        return {
          icon: <CheckCircle size={20} />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          iconColor: 'text-green-600',
          titleColor: 'text-green-800',
          messageColor: 'text-green-700'
        };
      case TOAST_TYPES.ERROR:
        return {
          icon: <XCircle size={20} />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconColor: 'text-red-600',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700'
        };
      case TOAST_TYPES.WARNING:
        return {
          icon: <AlertCircle size={20} />,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          iconColor: 'text-yellow-600',
          titleColor: 'text-yellow-800',
          messageColor: 'text-yellow-700'
        };
      case TOAST_TYPES.LOADING:
        return {
          icon: <Clock size={20} className="animate-spin" />,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          iconColor: 'text-blue-600',
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700'
        };
      default:
        return {
          icon: <Info size={20} />,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          iconColor: 'text-gray-600',
          titleColor: 'text-gray-800',
          messageColor: 'text-gray-700'
        };
    }
  };

  const config = getToastConfig();

  return (
    <div
      className={`
        relative max-w-sm w-full ${config.bgColor} border ${config.borderColor} rounded-lg shadow-lg p-4
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isExiting ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${isExiting ? 'scale-95' : 'scale-100'}
      `}
    >
      {/* Progress bar for loading toasts */}
      {type === TOAST_TYPES.LOADING && typeof progress === 'number' && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded-t-lg overflow-hidden">
          <div
            className="h-full bg-blue-500 transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className={`flex-shrink-0 ${config.iconColor}`}>
          {config.icon}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {title && (
            <h4 className={`text-sm font-semibold ${config.titleColor} mb-1`}>
              {title}
            </h4>
          )}
          {message && (
            <p className={`text-sm ${config.messageColor}`}>
              {message}
            </p>
          )}

          {/* Actions */}
          {actions.length > 0 && (
            <div className="flex space-x-2 mt-3">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={`text-xs px-3 py-1 rounded font-medium transition-colors ${
                    action.primary
                      ? `bg-${action.color || 'blue'}-600 text-white hover:bg-${action.color || 'blue'}-700`
                      : `text-${action.color || 'blue'}-600 hover:text-${action.color || 'blue'}-700 hover:bg-${action.color || 'blue'}-50`
                  }`}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Close button */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

/**
 * Toast Container Component
 */
export const ToastContainer = ({
  toasts = [],
  position = TOAST_POSITIONS.TOP_RIGHT,
  onRemoveToast
}) => {
  const getPositionClasses = () => {
    switch (position) {
      case TOAST_POSITIONS.TOP_RIGHT:
        return 'top-4 right-4';
      case TOAST_POSITIONS.TOP_LEFT:
        return 'top-4 left-4';
      case TOAST_POSITIONS.BOTTOM_RIGHT:
        return 'bottom-4 right-4';
      case TOAST_POSITIONS.BOTTOM_LEFT:
        return 'bottom-4 left-4';
      case TOAST_POSITIONS.TOP_CENTER:
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case TOAST_POSITIONS.BOTTOM_CENTER:
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  if (toasts.length === 0) { return null; }

  return (
    <div className={`fixed z-50 ${getPositionClasses()}`}>
      <div className="space-y-3">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            {...toast}
            onClose={onRemoveToast}
          />
        ))}
      </div>
    </div>
  );
};

/**
 * Toast Hook for managing toasts
 */
export const useToast = () => {
  const [toasts, setToasts] = useState([]);

  const addToast = useCallback((toastData) => {
    const id = Date.now() + Math.random();
    const toast = {
      id,
      ...toastData,
      timestamp: Date.now()
    };

    setToasts(prev => [...prev, toast]);
    return id;
  }, []);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const updateToast = useCallback((id, updates) => {
    setToasts(prev => prev.map(toast =>
      toast.id === id ? { ...toast, ...updates } : toast
    ));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const success = useCallback((title, message, options = {}) => {
    return addToast({ type: TOAST_TYPES.SUCCESS, title, message, ...options });
  }, [addToast]);

  const error = useCallback((title, message, options = {}) => {
    return addToast({ type: TOAST_TYPES.ERROR, title, message, duration: 0, ...options });
  }, [addToast]);

  const warning = useCallback((title, message, options = {}) => {
    return addToast({ type: TOAST_TYPES.WARNING, title, message, ...options });
  }, [addToast]);

  const info = useCallback((title, message, options = {}) => {
    return addToast({ type: TOAST_TYPES.INFO, title, message, ...options });
  }, [addToast]);

  const loading = useCallback((title, message, options = {}) => {
    return addToast({ type: TOAST_TYPES.LOADING, title, message, duration: 0, ...options });
  }, [addToast]);

  return {
    toasts,
    addToast,
    removeToast,
    updateToast,
    clearAllToasts,
    success,
    error,
    warning,
    info,
    loading
  };
};

export default Toast;
