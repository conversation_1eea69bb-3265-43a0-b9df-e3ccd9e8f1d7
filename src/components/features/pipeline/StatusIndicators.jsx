/**
 * Advanced Status Indicators for Pipeline Window
 * Provides rich visual feedback for pipeline states
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Pause,
  Play,
  Zap,
  TrendingUp,
  Server,
  Wifi,
  WifiOff
} from 'lucide-react';

/**
 * Pipeline Status Badge
 */
export const PipelineStatusBadge = ({
  status = 'idle',
  progress = 0,
  className = ''
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'running':
        return {
          icon: <Activity className="animate-pulse" size={16} />,
          label: 'Processing',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          borderColor: 'border-blue-200',
          pulseColor: 'bg-blue-500'
        };
      case 'completed':
        return {
          icon: <CheckCircle size={16} />,
          label: 'Completed',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-200'
        };
      case 'error':
        return {
          icon: <XCircle size={16} />,
          label: 'Error',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200'
        };
      case 'warning':
        return {
          icon: <AlertTriangle size={16} />,
          label: 'Warning',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-200'
        };
      case 'paused':
        return {
          icon: <Pause size={16} />,
          label: 'Paused',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200'
        };
      default:
        return {
          icon: <Clock size={16} />,
          label: 'Idle',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-600',
          borderColor: 'border-gray-200'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`
      relative inline-flex items-center space-x-2 px-3 py-1.5 rounded-full border
      ${config.bgColor} ${config.textColor} ${config.borderColor} ${className}
    `}>
      {/* Pulse animation for active states */}
      {status === 'running' && (
        <div className={`absolute inset-0 rounded-full ${config.pulseColor} opacity-20 animate-ping`} />
      )}

      <div className="relative z-10 flex items-center space-x-2">
        {config.icon}
        <span className="text-sm font-medium">{config.label}</span>
        {status === 'running' && progress > 0 && (
          <span className="text-xs opacity-75">({progress}%)</span>
        )}
      </div>
    </div>
  );
};

/**
 * Connection Status Indicator
 */
export const ConnectionStatus = ({ isOnline = true, apiStatus = 'connected' }) => {
  const [showDetails, setShowDetails] = useState(false);

  const getStatusConfig = () => {
    if (!isOnline) {
      return {
        icon: <WifiOff size={16} />,
        label: 'Offline',
        color: 'text-red-500',
        bgColor: 'bg-red-50'
      };
    }

    switch (apiStatus) {
      case 'connected':
        return {
          icon: <Wifi size={16} />,
          label: 'Connected',
          color: 'text-green-500',
          bgColor: 'bg-green-50'
        };
      case 'slow':
        return {
          icon: <Wifi size={16} className="animate-pulse" />,
          label: 'Slow Connection',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50'
        };
      case 'error':
        return {
          icon: <Server size={16} />,
          label: 'API Error',
          color: 'text-red-500',
          bgColor: 'bg-red-50'
        };
      default:
        return {
          icon: <Wifi size={16} />,
          label: 'Unknown',
          color: 'text-gray-500',
          bgColor: 'bg-gray-50'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className="relative">
      <button
        onClick={() => setShowDetails(!showDetails)}
        className={`
          flex items-center space-x-1 px-2 py-1 rounded-md transition-colors
          ${config.bgColor} ${config.color} hover:opacity-80
        `}
        title="Connection Status"
      >
        {config.icon}
        <span className="text-xs font-medium">{config.label}</span>
      </button>

      {/* Details tooltip */}
      {showDetails && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10 min-w-48">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600">Network:</span>
              <span className={`text-xs font-medium ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
                {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600">API Status:</span>
              <span className={`text-xs font-medium ${config.color}`}>
                {apiStatus}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Performance Metrics Display
 */
export const PerformanceMetrics = ({
  metrics = {},
  className = ''
}) => {
  const {
    processingTime = 0,
    memoryUsage = 0,
    cpuUsage = 0,
    throughput = 0
  } = metrics;

  const formatTime = (seconds) => {
    if (seconds < 60) { return `${seconds.toFixed(1)}s`; }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) { return '0 B'; }
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`;
  };

  return (
    <div className={`bg-gray-50 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        <TrendingUp size={16} className="text-gray-600" />
        <span className="text-sm font-medium text-gray-700">Performance</span>
      </div>

      <div className="grid grid-cols-2 gap-3 text-xs">
        <div className="space-y-1">
          <div className="flex justify-between">
            <span className="text-gray-600">Processing Time:</span>
            <span className="font-mono">{formatTime(processingTime)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Memory Usage:</span>
            <span className="font-mono">{formatBytes(memoryUsage)}</span>
          </div>
        </div>

        <div className="space-y-1">
          <div className="flex justify-between">
            <span className="text-gray-600">CPU Usage:</span>
            <span className="font-mono">{cpuUsage.toFixed(1)}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Throughput:</span>
            <span className="font-mono">{throughput.toFixed(1)}/s</span>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Step Status Timeline
 */
export const StepStatusTimeline = ({
  steps = [],
  currentStep = null,
  className = ''
}) => {
  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900 mb-3">Pipeline Timeline</h4>

      <div className="space-y-3">
        {steps.map((step, index) => {
          const isActive = currentStep === step.id;
          const isCompleted = step.status === 'completed';
          const hasError = step.status === 'error';

          return (
            <div key={step.id} className="flex items-center space-x-3">
              {/* Status indicator */}
              <div className={`
                w-3 h-3 rounded-full border-2 transition-colors
                ${isCompleted ? 'bg-green-500 border-green-500' :
              hasError ? 'bg-red-500 border-red-500' :
                isActive ? 'bg-blue-500 border-blue-500 animate-pulse' :
                  'bg-gray-200 border-gray-300'}
              `} />

              {/* Connection line */}
              {index < steps.length - 1 && (
                <div className={`
                  absolute left-[1.375rem] mt-3 w-0.5 h-6 transition-colors
                  ${isCompleted ? 'bg-green-200' : 'bg-gray-200'}
                `} />
              )}

              {/* Step info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className={`text-sm font-medium ${
                    isActive ? 'text-blue-700' :
                      isCompleted ? 'text-green-700' :
                        hasError ? 'text-red-700' :
                          'text-gray-500'
                  }`}>
                    {step.name}
                  </span>

                  {step.timing && (
                    <span className="text-xs text-gray-500 font-mono">
                      {step.timing.toFixed(1)}s
                    </span>
                  )}
                </div>

                {isActive && step.progress && (
                  <div className="mt-1">
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div
                        className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${step.progress}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * Real-time Activity Indicator
 */
export const ActivityIndicator = ({
  isActive = false,
  activityType = 'processing',
  className = ''
}) => {
  const [pulseCount, setPulseCount] = useState(0);

  useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        setPulseCount(prev => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isActive]);

  if (!isActive) { return null; }

  const getActivityConfig = () => {
    switch (activityType) {
      case 'processing':
        return {
          icon: <Zap className="animate-bounce" size={14} />,
          label: 'Processing',
          color: 'text-blue-500'
        };
      case 'uploading':
        return {
          icon: <TrendingUp className="animate-pulse" size={14} />,
          label: 'Uploading',
          color: 'text-green-500'
        };
      case 'analyzing':
        return {
          icon: <Activity className="animate-spin" size={14} />,
          label: 'Analyzing',
          color: 'text-purple-500'
        };
      default:
        return {
          icon: <Activity className="animate-pulse" size={14} />,
          label: 'Active',
          color: 'text-gray-500'
        };
    }
  };

  const config = getActivityConfig();

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={config.color}>
        {config.icon}
      </div>
      <span className="text-xs text-gray-600">
        {config.label}
        <span className="animate-pulse">...</span>
      </span>
      <div className="flex space-x-1">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className={`w-1 h-1 rounded-full ${config.color.replace('text-', 'bg-')} animate-pulse`}
            style={{ animationDelay: `${i * 0.2}s` }}
          />
        ))}
      </div>
    </div>
  );
};

export default PipelineStatusBadge;
