/**
 * Window Controls and Settings for Pipeline Window
 * Provides window management and customization options
 */

import React, { useState, useEffect } from 'react';
import {
  Settings,
  Maximize2,
  Minimize2,
  RotateCcw,
  Download,
  Upload,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Palette,
  Monitor,
  Sun,
  Moon
} from 'lucide-react';

/**
 * Window Settings Hook
 */
export const useWindowSettings = () => {
  const [settings, setSettings] = useState({
    theme: 'light',
    soundEnabled: true,
    autoSave: true,
    showPerformanceMetrics: true,
    showKeyboardShortcuts: true,
    animationsEnabled: true,
    compactMode: false,
    autoExportLogs: false,
    notificationsEnabled: true
  });

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('pipelineWindowSettings');
    if (savedSettings) {
      try {
        setSettings(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
      } catch (error) {
        console.warn('Failed to load window settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  const updateSetting = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    localStorage.setItem('pipelineWindowSettings', JSON.stringify(newSettings));
  };

  const resetSettings = () => {
    const defaultSettings = {
      theme: 'light',
      soundEnabled: true,
      autoSave: true,
      showPerformanceMetrics: true,
      showKeyboardShortcuts: true,
      animationsEnabled: true,
      compactMode: false,
      autoExportLogs: false,
      notificationsEnabled: true
    };
    setSettings(defaultSettings);
    localStorage.setItem('pipelineWindowSettings', JSON.stringify(defaultSettings));
  };

  return {
    settings,
    updateSetting,
    resetSettings
  };
};

/**
 * Window Control Bar Component
 */
export const WindowControlBar = ({
  onMinimize,
  onMaximize,
  onClose,
  onSettings,
  onExport,
  isMaximized = false,
  className = ''
}) => {
  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {/* Export button */}
      <button
        onClick={onExport}
        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
        title="Export Pipeline Data"
      >
        <Download size={16} />
      </button>

      {/* Settings button */}
      <button
        onClick={onSettings}
        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
        title="Window Settings"
      >
        <Settings size={16} />
      </button>

      {/* Minimize button */}
      <button
        onClick={onMinimize}
        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
        title="Minimize Window"
      >
        <Minimize2 size={16} />
      </button>

      {/* Maximize/Restore button */}
      <button
        onClick={onMaximize}
        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
        title={isMaximized ? 'Restore Window' : 'Maximize Window'}
      >
        <Maximize2 size={16} />
      </button>
    </div>
  );
};

/**
 * Settings Panel Component
 */
export const SettingsPanel = ({
  isOpen = false,
  onClose,
  settings = {},
  onUpdateSetting,
  onResetSettings,
  className = ''
}) => {
  if (!isOpen) { return null; }

  const settingsGroups = [
    {
      title: 'Appearance',
      settings: [
        {
          key: 'theme',
          label: 'Theme',
          type: 'select',
          options: [
            { value: 'light', label: 'Light', icon: <Sun size={16} /> },
            { value: 'dark', label: 'Dark', icon: <Moon size={16} /> },
            { value: 'auto', label: 'Auto', icon: <Monitor size={16} /> }
          ]
        },
        {
          key: 'animationsEnabled',
          label: 'Enable Animations',
          type: 'toggle',
          description: 'Enable smooth transitions and animations'
        },
        {
          key: 'compactMode',
          label: 'Compact Mode',
          type: 'toggle',
          description: 'Use compact layout to save space'
        }
      ]
    },
    {
      title: 'Features',
      settings: [
        {
          key: 'showPerformanceMetrics',
          label: 'Performance Metrics',
          type: 'toggle',
          description: 'Show real-time performance monitoring'
        },
        {
          key: 'showKeyboardShortcuts',
          label: 'Keyboard Shortcuts',
          type: 'toggle',
          description: 'Enable keyboard shortcuts and hints'
        },
        {
          key: 'notificationsEnabled',
          label: 'Notifications',
          type: 'toggle',
          description: 'Show toast notifications for events'
        },
        {
          key: 'soundEnabled',
          label: 'Sound Effects',
          type: 'toggle',
          description: 'Play sounds for important events'
        }
      ]
    },
    {
      title: 'Data',
      settings: [
        {
          key: 'autoSave',
          label: 'Auto Save',
          type: 'toggle',
          description: 'Automatically save pipeline progress'
        },
        {
          key: 'autoExportLogs',
          label: 'Auto Export Logs',
          type: 'toggle',
          description: 'Automatically export logs on completion'
        }
      ]
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Settings className="text-blue-600" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">Window Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-8">
            {settingsGroups.map(group => (
              <div key={group.title}>
                <h3 className="text-lg font-medium text-gray-900 mb-4">{group.title}</h3>
                <div className="space-y-4">
                  {group.settings.map(setting => (
                    <div key={setting.key} className="flex items-center justify-between">
                      <div className="flex-1">
                        <label className="text-sm font-medium text-gray-700">
                          {setting.label}
                        </label>
                        {setting.description && (
                          <p className="text-xs text-gray-500 mt-1">{setting.description}</p>
                        )}
                      </div>

                      <div className="ml-4">
                        {setting.type === 'toggle' && (
                          <button
                            onClick={() => onUpdateSetting(setting.key, !settings[setting.key])}
                            className={`
                              relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                              ${settings[setting.key] ? 'bg-blue-600' : 'bg-gray-200'}
                            `}
                          >
                            <span
                              className={`
                                inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                                ${settings[setting.key] ? 'translate-x-6' : 'translate-x-1'}
                              `}
                            />
                          </button>
                        )}

                        {setting.type === 'select' && (
                          <select
                            value={settings[setting.key]}
                            onChange={(e) => onUpdateSetting(setting.key, e.target.value)}
                            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            {setting.options.map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <button
            onClick={onResetSettings}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <RotateCcw size={16} />
            <span>Reset to Defaults</span>
          </button>

          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Quick Settings Toggle
 */
export const QuickSettingsToggle = ({
  settings = {},
  onUpdateSetting,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const quickSettings = [
    {
      key: 'showPerformanceMetrics',
      label: 'Performance',
      icon: settings.showPerformanceMetrics ? <Eye size={16} /> : <EyeOff size={16} />
    },
    {
      key: 'soundEnabled',
      label: 'Sound',
      icon: settings.soundEnabled ? <Volume2 size={16} /> : <VolumeX size={16} />
    },
    {
      key: 'animationsEnabled',
      label: 'Animations',
      icon: <Palette size={16} />
    }
  ];

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
        title="Quick Settings"
      >
        <Settings size={16} />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10 min-w-40">
          <div className="space-y-1">
            {quickSettings.map(setting => (
              <button
                key={setting.key}
                onClick={() => {
                  onUpdateSetting(setting.key, !settings[setting.key]);
                  setIsOpen(false);
                }}
                className={`
                  w-full flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors
                  ${settings[setting.key]
                ? 'bg-blue-50 text-blue-700'
                : 'text-gray-600 hover:bg-gray-50'
              }
                `}
              >
                {setting.icon}
                <span>{setting.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WindowControlBar;
