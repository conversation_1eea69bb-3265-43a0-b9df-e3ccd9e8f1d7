/**
 * PipelineStepCard - Individual step component for the multi-step pipeline
 * Displays step status, progress, and action buttons with enhanced modern styling
 */

import React, { useState, useEffect } from 'react';
import { getStepColors, ACTION_TYPES, STEP_STATUS } from '../../../core/config/pipelineSteps.js';

// Add CSS animations for enhanced effects
const shimmerKeyframes = `
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }
`;

const PipelineStepCard = ({
  step,
  status = STEP_STATUS.PENDING,
  progress = 0,
  result = null,
  error = null,
  timing = 0,
  onAction,
  isActive = false,
  showDetails = false,
  showExpandableData = false,
  compactMode = false, // New prop for compact display
  windowMode = false // New prop for window mode with inline styles
}) => {
  const [expanded, setExpanded] = useState(showDetails);
  const [showRawInput, setShowRawInput] = useState(false);
  const [showRawOutput, setShowRawOutput] = useState(false);
  const colors = getStepColors(step.color);

  // Inject CSS animations on component mount
  useEffect(() => {
    if (!document.getElementById('pipeline-animations')) {
      const style = document.createElement('style');
      style.id = 'pipeline-animations';
      style.textContent = shimmerKeyframes;
      document.head.appendChild(style);
    }
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case STEP_STATUS.COMPLETED:
        return '✅';
      case STEP_STATUS.RUNNING:
        return '⏳';
      case STEP_STATUS.ERROR:
        return '❌';
      case STEP_STATUS.SKIPPED:
        return '⏭️';
      default:
        return '⏸️';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case STEP_STATUS.COMPLETED:
        return 'text-green-600';
      case STEP_STATUS.RUNNING:
        return 'text-blue-600';
      case STEP_STATUS.ERROR:
        return 'text-red-600';
      case STEP_STATUS.SKIPPED:
        return 'text-gray-500';
      default:
        return 'text-gray-400';
    }
  };

  const handleAction = (actionType) => {
    // Handle fold/unfold actions locally
    if (actionType === ACTION_TYPES.VIEW_RAW) {
      setShowRawInput(!showRawInput);
      return;
    }
    if (actionType === ACTION_TYPES.VIEW_OUTPUT) {
      setShowRawOutput(!showRawOutput);
      return;
    }

    // Pass other actions to parent
    if (onAction) {
      onAction(step.id, actionType);
    }
  };

  const formatTiming = (ms) => {
    if (ms < 1000) { return `${ms}ms`; }
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getActionButtons = () => {
    const buttons = [];

    step.actions.forEach(action => {
      let label, variant, disabled;

      switch (action) {
        case ACTION_TYPES.RERUN:
          label = '🔄 Rerun';
          variant = 'secondary';
          disabled = status === STEP_STATUS.RUNNING;
          break;
        case ACTION_TYPES.VIEW_RAW:
          label = showRawInput ? '📄 Hide Input' : '📄 Show Input';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.VIEW_OUTPUT:
          label = showRawOutput ? '📊 Hide Output' : '📊 Show Output';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.ENHANCE_PROMPT:
          label = '✨ Enhance';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        case ACTION_TYPES.VIEW_SIMILAR:
          label = '🔗 Similar';
          variant = 'outline';
          disabled = !result?.similar_docs;
          break;
        case ACTION_TYPES.COMPARE_PDF:
          label = '🔍 Compare';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.VIEW_MAPPING:
          label = '🗺️ Mapping';
          variant = 'outline';
          disabled = !result?.mapped_fields;
          break;
        case ACTION_TYPES.VIEW_ERRORS:
          label = '⚠️ Errors';
          variant = 'outline';
          disabled = !result?.errors?.length;
          break;
        case ACTION_TYPES.EXPORT:
          label = '💾 Export';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        case ACTION_TYPES.SAVE:
          label = '💾 Save';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        default:
          return;
      }

      // Enhanced modern button design system
      const getButtonClasses = () => {
        const baseClasses = 'inline-flex items-center px-4 py-2 text-xs font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 transform hover:scale-105 active:scale-95';

        if (disabled) {
          return `${baseClasses} bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200 opacity-50`;
        }

        switch (variant) {
          case 'primary':
            return `${baseClasses} bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500 shadow-lg hover:shadow-xl shadow-blue-500/25`;
          case 'secondary':
            return `${baseClasses} bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 focus:ring-gray-500 shadow-lg hover:shadow-xl shadow-gray-500/25`;
          case 'outline':
          default:
            return `${baseClasses} bg-white/80 backdrop-blur-sm text-gray-700 border border-gray-300 hover:bg-white hover:border-gray-400 focus:ring-gray-500 shadow-md hover:shadow-lg`;
        }
      };

      buttons.push(
        <button
          key={action}
          onClick={() => handleAction(action)}
          disabled={disabled}
          className={getButtonClasses()}
          title={disabled ? 'Action not available' : `${label.replace(/[^\w\s]/g, '').trim()}`}
        >
          {label}
        </button>
      );
    });

    return buttons;
  };

  // Render compact mode for better UX - Enhanced modern design
  if (compactMode) {
    const getContainerStyle = () => {
      if (!windowMode) { return {}; }

      let backgroundColor = 'rgba(255, 255, 255, 0.95)';
      let borderColor = '#e5e7eb';
      let boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
      let backdropFilter = 'blur(8px)';

      if (isActive) {
        backgroundColor = 'rgba(239, 246, 255, 0.95)';
        borderColor = '#3b82f6';
        boxShadow = '0 8px 25px -5px rgba(59, 130, 246, 0.25), 0 4px 6px -2px rgba(59, 130, 246, 0.1), 0 0 0 1px rgba(59, 130, 246, 0.2)';
      } else if (status === STEP_STATUS.ERROR) {
        backgroundColor = 'rgba(254, 242, 242, 0.95)';
        borderColor = '#ef4444';
        boxShadow = '0 8px 25px -5px rgba(239, 68, 68, 0.25), 0 4px 6px -2px rgba(239, 68, 68, 0.1)';
      } else if (status === STEP_STATUS.COMPLETED) {
        backgroundColor = 'rgba(240, 253, 244, 0.95)';
        borderColor = '#22c55e';
        boxShadow = '0 8px 25px -5px rgba(34, 197, 94, 0.25), 0 4px 6px -2px rgba(34, 197, 94, 0.1)';
      } else if (status === STEP_STATUS.RUNNING) {
        backgroundColor = 'rgba(239, 246, 255, 0.95)';
        borderColor = '#3b82f6';
        boxShadow = '0 8px 25px -5px rgba(59, 130, 246, 0.25), 0 4px 6px -2px rgba(59, 130, 246, 0.1)';
      }

      return {
        position: 'relative',
        padding: '1.5rem',
        borderRadius: '1rem',
        border: `1px solid ${borderColor}`,
        backgroundColor,
        backdropFilter,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        boxShadow,
        fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        overflow: 'hidden'
      };
    };

    const getStatusBarStyle = () => {
      if (!windowMode) { return {}; }

      let background = 'linear-gradient(180deg, #d1d5db 0%, #9ca3af 100%)';
      if (status === STEP_STATUS.COMPLETED) {
        background = 'linear-gradient(180deg, #22c55e 0%, #16a34a 100%)';
      } else if (status === STEP_STATUS.RUNNING) {
        background = 'linear-gradient(180deg, #3b82f6 0%, #2563eb 100%)';
      } else if (status === STEP_STATUS.ERROR) {
        background = 'linear-gradient(180deg, #ef4444 0%, #dc2626 100%)';
      }

      return {
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        width: '5px',
        borderRadius: '1rem 0 0 1rem',
        background,
        boxShadow: status !== STEP_STATUS.PENDING ? 'inset 0 1px 2px rgba(255, 255, 255, 0.3)' : 'none'
      };
    };

    return (
      <div style={windowMode ? getContainerStyle() : {}} className={!windowMode ? `
        relative p-6 rounded-2xl border transition-all duration-300 hover:shadow-xl bg-white/95 backdrop-blur-sm group hover:-translate-y-1
        ${isActive ? 'ring-2 ring-blue-500/30 shadow-2xl border-blue-400 bg-gradient-to-br from-blue-50/60 to-blue-100/40' : 'border-gray-200/60 hover:border-gray-300/80 shadow-lg hover:shadow-xl'}
        ${status === STEP_STATUS.ERROR ? 'border-red-400/60 bg-gradient-to-br from-red-50/60 to-red-100/40 hover:shadow-red-200/50' : ''}
        ${status === STEP_STATUS.COMPLETED ? 'border-green-400/60 bg-gradient-to-br from-green-50/60 to-green-100/40 hover:shadow-green-200/50' : ''}
        ${status === STEP_STATUS.RUNNING ? 'border-blue-400/60 bg-gradient-to-br from-blue-50/60 to-blue-100/40 hover:shadow-blue-200/50' : ''}
      ` : ''}>
        {/* Enhanced status indicator bar with gradient */}
        <div style={windowMode ? getStatusBarStyle() : {}} className={!windowMode ? `absolute left-0 top-0 bottom-0 w-1.5 rounded-l-2xl ${
          status === STEP_STATUS.COMPLETED ? 'bg-gradient-to-b from-green-400 to-green-600' :
            status === STEP_STATUS.RUNNING ? 'bg-gradient-to-b from-blue-400 to-blue-600' :
              status === STEP_STATUS.ERROR ? 'bg-gradient-to-b from-red-400 to-red-600' :
                'bg-gradient-to-b from-gray-300 to-gray-400'
        }` : ''} />
        {/* Compact Header */}
        <div style={windowMode ? {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        } : {}} className={!windowMode ? 'flex items-center justify-between' : ''}>
          <div style={windowMode ? {
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          } : {}} className={!windowMode ? 'flex items-center space-x-4' : ''}>
            <div style={windowMode ? {
              padding: '0.75rem',
              borderRadius: '0.75rem',
              background:
                status === STEP_STATUS.COMPLETED ? 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)' :
                  status === STEP_STATUS.RUNNING ? 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)' :
                    status === STEP_STATUS.ERROR ? 'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)' :
                      'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
              color:
                status === STEP_STATUS.COMPLETED ? '#16a34a' :
                  status === STEP_STATUS.RUNNING ? '#2563eb' :
                    status === STEP_STATUS.ERROR ? '#dc2626' :
                      '#9ca3af',
              boxShadow: 'inset 0 1px 2px rgba(255, 255, 255, 0.5), 0 2px 4px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              transform: status === STEP_STATUS.RUNNING ? 'scale(1.05)' : 'scale(1)'
            } : {}} className={!windowMode ? `p-3 rounded-xl transition-all duration-300 ${
              status === STEP_STATUS.COMPLETED ? 'bg-gradient-to-br from-green-100 to-green-200 text-green-600 shadow-lg shadow-green-200/50' :
                status === STEP_STATUS.RUNNING ? 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600 shadow-lg shadow-blue-200/50 scale-105' :
                  status === STEP_STATUS.ERROR ? 'bg-gradient-to-br from-red-100 to-red-200 text-red-600 shadow-lg shadow-red-200/50' :
                    'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-400 shadow-md'
            }` : ''}>
              <span style={windowMode ? {
                fontSize: '1.25rem',
                filter: status === STEP_STATUS.RUNNING ? 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))' : 'none'
              } : {}} className={!windowMode ? `text-xl ${status === STEP_STATUS.RUNNING ? 'animate-pulse' : ''}` : ''}>{step.icon}</span>
            </div>
            <div style={windowMode ? {
              flex: 1,
              minWidth: 0
            } : {}} className={!windowMode ? 'flex-1 min-w-0' : ''}>
              <h3 style={windowMode ? {
                fontWeight: '600',
                fontSize: '1rem',
                color: '#111827',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              } : {}} className={!windowMode ? 'font-semibold text-base text-gray-900 truncate' : ''}>{step.name}</h3>
              <p style={windowMode ? {
                fontSize: '0.875rem',
                color: '#4b5563',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              } : {}} className={!windowMode ? 'text-sm text-gray-600 truncate' : ''}>{step.description}</p>
              {status === STEP_STATUS.RUNNING && (
                <div style={windowMode ? {
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginTop: '0.5rem'
                } : {}} className={!windowMode ? 'flex items-center space-x-2 mt-2' : ''}>
                  <div style={windowMode ? {
                    width: '100px',
                    backgroundColor: '#e5e7eb',
                    borderRadius: '9999px',
                    height: '10px',
                    overflow: 'hidden',
                    boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  } : {}} className={!windowMode ? 'w-24 bg-gray-200 rounded-full h-3 overflow-hidden shadow-inner border border-white/20' : ''}>
                    <div
                      style={windowMode ? {
                        background: 'linear-gradient(90deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)',
                        height: '10px',
                        borderRadius: '9999px',
                        transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
                        width: `${typeof progress === 'number' ? progress : 0}%`,
                        position: 'relative',
                        boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)'
                      } : { width: `${typeof progress === 'number' ? progress : 0}%` }}
                      className={!windowMode ? 'bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 h-3 rounded-full transition-all duration-600 relative shadow-lg shadow-blue-500/50' : ''}
                    >
                      {windowMode && (
                        <div style={{
                          position: 'absolute',
                          inset: 0,
                          background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%)',
                          borderRadius: '9999px',
                          animation: 'shimmer 2s infinite'
                        }} />
                      )}
                      {!windowMode && (
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full animate-shimmer" />
                      )}
                    </div>
                  </div>
                  <span style={windowMode ? {
                    fontSize: '0.75rem',
                    color: '#4b5563',
                    fontWeight: '500',
                    backgroundColor: '#f3f4f6',
                    padding: '0.125rem 0.5rem',
                    borderRadius: '0.25rem'
                  } : {}} className={!windowMode ? 'text-xs text-gray-600 font-medium bg-gray-100 px-2 py-0.5 rounded' : ''}>
                    {typeof progress === 'number' ? progress : 0}%
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full transition-all duration-300 ${
              status === STEP_STATUS.COMPLETED ? 'bg-gradient-to-br from-green-100 to-green-200 shadow-lg shadow-green-200/50 ring-2 ring-green-300/30' :
                status === STEP_STATUS.RUNNING ? 'bg-gradient-to-br from-blue-100 to-blue-200 shadow-lg shadow-blue-200/50 ring-2 ring-blue-300/30 animate-pulse' :
                  status === STEP_STATUS.ERROR ? 'bg-gradient-to-br from-red-100 to-red-200 shadow-lg shadow-red-200/50 ring-2 ring-red-300/30' :
                    'bg-gradient-to-br from-gray-100 to-gray-200 shadow-md'
            }`}>
              <span className={`text-xl transition-all duration-300 ${
                status === STEP_STATUS.RUNNING ? 'animate-bounce' : ''
              }`}>{getStatusIcon()}</span>
            </div>
            {(result || error) && (
              <button
                onClick={() => setExpanded(!expanded)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 group-hover:opacity-100 opacity-60"
                title="View details"
              >
                <span className="text-sm">{expanded ? '▼' : '▶'}</span>
              </button>
            )}
          </div>
        </div>

        {/* Compact Status - Extension styled */}
        <div className="flex items-center justify-between mt-2 text-xs">
          <span className={`font-medium ${
            status === STEP_STATUS.COMPLETED ? 'text-success-600' :
              status === STEP_STATUS.ERROR ? 'text-error-600' :
                status === STEP_STATUS.RUNNING ? 'text-primary-600' :
                  'text-gray-500'
          }`}>
            {timing > 0 ? `${formatTiming(timing)}` :
              status === STEP_STATUS.RUNNING ? 'Processing...' :
                status === STEP_STATUS.COMPLETED ? 'Completed' :
                  status === STEP_STATUS.ERROR ? 'Failed' : 'Pending'}
          </span>
          {result && result.confidence && (
            <span className="text-success-600 font-medium bg-success-100 px-2 py-0.5 rounded-full">
              {result.confidence}%
            </span>
          )}
        </div>

        {/* Error Display - Compact with extension styling */}
        {error && (
          <div className="mt-2 p-2 bg-error-50 border border-error-200 rounded-md text-xs text-error-700">
            <strong className="text-error-800">Error:</strong> {typeof error === 'string' ? error.substring(0, 100) + (error.length > 100 ? '...' : '') : 'Processing failed'}
          </div>
        )}

        {/* Expanded Details - Only when requested */}
        {expanded && (result || error) && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="space-y-2">
              {/* Quick Actions */}
              <div className="flex flex-wrap gap-1">
                {getActionButtons()}
              </div>

              {/* Summary Info */}
              {result && result.summary && (
                <div className="text-xs">
                  <strong className="text-gray-700">Summary:</strong>
                  <p className="text-gray-600 mt-1">{result.summary}</p>
                </div>
              )}

              {/* Detailed Error */}
              {error && (
                <div className="text-xs">
                  <strong className="text-red-700">Full Error:</strong>
                  <pre className="text-red-600 mt-1 whitespace-pre-wrap bg-red-50 p-2 rounded max-h-32 overflow-auto">
                    {typeof error === 'string' ? error : JSON.stringify(error, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Raw Data Sections - Only when specifically requested */}
        {showRawInput && result && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Raw Input Data</h4>
              <button
                onClick={() => setShowRawInput(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-auto">
              <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                {JSON.stringify(result.input || result.rawInput || 'No input data available', null, 2)}
              </pre>
            </div>
          </div>
        )}

        {showRawOutput && result && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Raw Output Data</h4>
              <button
                onClick={() => setShowRawOutput(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-auto">
              <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Full detailed mode (existing implementation)
  return (
    <div className={`
      relative p-4 rounded-lg border-2 transition-all duration-200
      ${colors.bg} ${colors.border}
      ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
      ${status === STEP_STATUS.ERROR ? 'border-red-300 bg-red-50' : ''}
    `}>
      {/* Step Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className={`text-lg ${colors.icon}`}>{step.icon}</span>
          <div>
            <h3 className={`font-medium ${colors.text}`}>{step.name}</h3>
            <p className="text-xs text-gray-600">{step.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`text-sm ${getStatusColor()}`}>{getStatusIcon()}</span>
          <button
            onClick={() => setExpanded(!expanded)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            {expanded ? '▼' : '▶'}
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {status === STEP_STATUS.RUNNING && (
        <div className="mb-2">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full transition-all duration-300 ${colors.progress}`}
              style={{ width: `${typeof progress === 'number' ? progress : 0}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">{typeof progress === 'number' ? progress : 0}%</div>
        </div>
      )}

      {/* Timing and Status */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
        <span>
          {timing > 0 ? `Completed in ${formatTiming(timing)}` :
            status === STEP_STATUS.RUNNING ? 'Processing...' :
              `Est. ${formatTiming(step.estimatedTime)}`}
        </span>
        {result && (
          <span className="text-green-600">
            {result.confidence ? `${result.confidence}% confidence` : 'Completed'}
          </span>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
          <strong>Error:</strong> {typeof error === 'string' ? error : JSON.stringify(error)}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-1 mb-2">
        {getActionButtons()}
      </div>

      {/* Expanded Details */}
      {expanded && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="text-xs space-y-2">
            <div>
              <strong className="text-gray-700">Outputs:</strong>
              <div className="mt-1 space-y-1">
                {step.outputs.map(output => (
                  <div key={output} className="flex justify-between">
                    <span className="text-gray-600">{output}:</span>
                    <span className="text-gray-800">
                      {result[output] ? '✓' : '✗'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {result.summary && (
              <div>
                <strong className="text-gray-700">Summary:</strong>
                <p className="text-gray-600 mt-1">{result.summary}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Raw Input Section */}
      {showRawInput && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Raw Input Data</h4>
            <button
              onClick={() => setShowRawInput(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(result.input || result.rawInput || 'No input data available', null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Raw Output Section */}
      {showRawOutput && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Raw Output Data</h4>
            <button
              onClick={() => setShowRawOutput(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default PipelineStepCard;
