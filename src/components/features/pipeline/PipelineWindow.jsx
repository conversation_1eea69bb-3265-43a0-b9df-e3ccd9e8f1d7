/**
 * Pipeline Modal Component
 * Opens multi-step pipeline in a full-screen modal within the extension popup
 * Replaces separate window approach with integrated modal experience
 */

import React, { useEffect, useRef, useState } from 'react';
import { createRoot } from 'react-dom/client';
import EnhancedPipelineVisualization, { VIEW_MODES } from './EnhancedPipelineVisualization.jsx';
import { ToastContainer, useToast } from '../../ui/feedback/ToastNotification.jsx';
import { useKeyboardShortcuts, KeyboardShortcutsHelp, KeyboardShortcutIndicator } from './KeyboardShortcuts.jsx';
import { PipelineStatusBadge, ConnectionStatus, PerformanceMetrics, ActivityIndicator } from './StatusIndicators.jsx';

/**
 * Pipeline Modal Manager
 * Handles opening and managing full-screen modals for pipeline processing
 */
export class PipelineModalManager {
  constructor() {
    this.modals = new Map(); // Track open modals by file ID
    this.modalCallbacks = new Map(); // Track modal callbacks
  }

  /**
   * Open pipeline in full-screen modal
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   * @param {Function} onModalStateChange - Callback for modal state changes
   * @returns {string} - Modal ID for tracking
   */
  openPipelineModal(file, options = {}, onModalStateChange = null) {
    const fileId = file?.name || `pipeline-${Date.now()}`;

    // Close existing modal for this file if open
    if (this.modals.has(fileId)) {
      this.closePipelineModal(fileId);
    }

    // Store modal data and callbacks
    const modalData = {
      file,
      options,
      timestamp: Date.now(),
      fileId,
      isOpen: true
    };

    this.modals.set(fileId, modalData);

    if (onModalStateChange) {
      this.modalCallbacks.set(fileId, onModalStateChange);
      // Notify that modal is opening
      onModalStateChange(true, modalData);
    }

    return fileId;
  }

  /**
   * Get modal data for specific file
   * @param {string} fileId - File identifier
   * @returns {Object|null} - Modal data or null
   */
  getModalData(fileId) {
    return this.modals.get(fileId) || null;
  }

  /**
   * Update modal data for specific file
   * @param {string} fileId - File identifier
   * @param {Object} updates - Data updates
   */
  updateModalData(fileId, updates) {
    const modalData = this.modals.get(fileId);
    if (modalData) {
      Object.assign(modalData, updates);
      this.modals.set(fileId, modalData);
    }
  }

  /**
   * Close pipeline modal for specific file
   * @param {string} fileId - File identifier
   */
  closePipelineModal(fileId) {
    const modalData = this.modals.get(fileId);
    if (modalData) {
      modalData.isOpen = false;
      this.modals.delete(fileId);

      // Notify callback about modal closing
      const callback = this.modalCallbacks.get(fileId);
      if (callback) {
        callback(false, modalData);
        this.modalCallbacks.delete(fileId);
      }
    }
  }

  /**
   * Close all pipeline modals
   */
  closeAllModals() {
    for (const [fileId] of this.modals) {
      this.closePipelineModal(fileId);
    }
  }

  /**
   * Get modal for specific file
   * @param {string} fileId - File identifier
   * @returns {Object|null} - Modal data or null
   */
  getModal(fileId) {
    const modalData = this.modals.get(fileId);
    return (modalData && modalData.isOpen) ? modalData : null;
  }

  /**
   * Check if modal is open for file
   * @param {string} fileId - File identifier
   * @returns {boolean} - True if modal is open
   */
  isModalOpen(fileId) {
    return this.getModal(fileId) !== null;
  }

  /**
   * Get all open modals
   * @returns {Array} - Array of open modal data
   */
  getAllOpenModals() {
    return Array.from(this.modals.values()).filter(modal => modal.isOpen);
  }

}

// Global instance
export const pipelineModalManager = new PipelineModalManager();

/**
 * Full-Screen Pipeline Modal Component
 * Renders the pipeline processing interface as a full-screen modal
 */
export function PipelineFullScreenModal({
  isOpen,
  file,
  options = {},
  onClose
}) {
  const [overallProgress, setOverallProgress] = useState(0);
  const [pipelineStatus, setPipelineStatus] = useState('idle');
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false);

  // Toast system
  const toast = useToast();

  // Keyboard shortcuts
  const { activeShortcuts } = useKeyboardShortcuts({
    SHOW_SHORTCUTS: () => setShowShortcutsHelp(true),
    CLOSE_MODAL: () => onClose?.(),
    TOGGLE_VIEW_MODE: () => {
      toast.info('View Mode', 'Toggled view mode');
    }
  });

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="w-full h-full bg-white flex flex-col">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center space-x-3">
            <div className="text-xl">🔄</div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Pipeline: {file?.name || 'Processing'}
              </h2>
              <p className="text-sm text-gray-500">
                Real-time document processing pipeline
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Status indicators */}
            <div className="flex items-center space-x-2">
              <PipelineStatusBadge
                status={pipelineStatus}
                progress={overallProgress}
              />
              <ConnectionStatus
                isOnline={navigator.onLine}
                apiStatus="connected"
              />
            </div>

            <button
              onClick={onClose}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
            >
              ✕ Close
            </button>
          </div>

          {/* Progress bar */}
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-200">
            <div
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>

        {/* Modal Content */}
        <div className="flex-1 overflow-hidden bg-gray-50">
          <EnhancedPipelineVisualization
            file={file}
            isProcessing={options.isProcessing || false}
            onProcessingChange={(processing, progress) => {
              options.onProcessingChange?.(processing, progress);
              if (typeof progress === 'number') {
                setOverallProgress(progress);
              }
              setPipelineStatus(processing ? 'running' : 'idle');
            }}
            onStepComplete={(stepId, result) => {
              options.onStepComplete?.(stepId, result);
              toast.success('Step Completed', `${stepId} finished successfully`);
            }}
            onError={(error) => {
              options.onError?.(error);
              setPipelineStatus('error');
              toast.error('Pipeline Error', error.message || 'An error occurred');
            }}
            autoRun={options.autoRun !== false}
            initialViewMode={VIEW_MODES.DETAILED}
            modalMode={true}
          />
        </div>

        {/* Toast notifications */}
        <ToastContainer
          toasts={toast.toasts}
          onRemoveToast={toast.removeToast}
          position="top-right"
        />

        {/* Keyboard shortcuts help */}
        <KeyboardShortcutsHelp
          isOpen={showShortcutsHelp}
          onClose={() => setShowShortcutsHelp(false)}
        />

        {/* Active shortcuts indicator */}
        <KeyboardShortcutIndicator activeShortcuts={activeShortcuts} />
      </div>
    </div>
  );
}

/**
 * React Hook for Pipeline Modal Management
 * @returns {Object} - Modal management functions
 */
export function usePipelineModal() {
  const [openModals, setOpenModals] = useState(new Set());

  const openModal = (file, options = {}) => {
    const fileId = file?.name || `pipeline-${Date.now()}`;

    const modalId = pipelineModalManager.openPipelineModal(file, options, (isOpen, modalData) => {
      if (isOpen) {
        setOpenModals(prev => new Set([...prev, fileId]));
      } else {
        setOpenModals(prev => {
          const newSet = new Set(prev);
          newSet.delete(fileId);
          return newSet;
        });
      }
    });

    return modalId;
  };

  const closeModal = (fileId) => {
    pipelineModalManager.closePipelineModal(fileId);
    setOpenModals(prev => {
      const newSet = new Set(prev);
      newSet.delete(fileId);
      return newSet;
    });
  };

  const closeAllModals = () => {
    pipelineModalManager.closeAllModals();
    setOpenModals(new Set());
  };

  const isModalOpen = (fileId) => {
    return openModals.has(fileId) && pipelineModalManager.isModalOpen(fileId);
  };

  const getModalData = (fileId) => {
    return pipelineModalManager.getModalData(fileId);
  };

  return {
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
    getModalData,
    openModals: Array.from(openModals)
  };
}

// Backward compatibility - keep the old hook name but use modal functionality
export function usePipelineWindow() {
  const modalHook = usePipelineModal();

  return {
    openWindow: modalHook.openModal,
    closeWindow: modalHook.closeModal,
    closeAllWindows: modalHook.closeAllModals,
    isWindowOpen: modalHook.isModalOpen,
    openWindows: modalHook.openModals
  };
}

export default PipelineModalManager;
