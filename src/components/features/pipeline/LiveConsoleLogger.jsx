/**
 * Live Console Logger Component
 * Real-time console logging with filtering, search, and export capabilities
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  Filter,
  Download,
  Trash2,
  Copy,
  ChevronDown,
  ChevronRight,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Terminal
} from 'lucide-react';

const LiveConsoleLogger = ({
  logs = [],
  isProcessing = false,
  onClear = null,
  onExport = null,
  maxLogs = 1000,
  autoScroll = true,
  compact = false,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('all');
  const [stepFilter, setStepFilter] = useState('all');
  const [expandedLogs, setExpandedLogs] = useState(new Set());
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(autoScroll);

  const consoleRef = useRef(null);
  const bottomRef = useRef(null);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (isAutoScrollEnabled && bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, isAutoScrollEnabled]);

  // Filter logs based on search and filters
  const filteredLogs = logs.filter(log => {
    const matchesSearch = !searchTerm ||
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.data && JSON.stringify(log.data).toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesLevel = levelFilter === 'all' || log.level === levelFilter;
    const matchesStep = stepFilter === 'all' || log.step === stepFilter;

    return matchesSearch && matchesLevel && matchesStep;
  });

  // Get unique steps for filter dropdown
  const uniqueSteps = [...new Set(logs.map(log => log.step).filter(Boolean))];

  // Get log level icon and color
  const getLogLevelInfo = (level) => {
    switch (level) {
      case 'error':
        return { icon: <XCircle size={16} />, color: 'text-red-600', bg: 'bg-red-50' };
      case 'warning':
        return { icon: <AlertCircle size={16} />, color: 'text-yellow-600', bg: 'bg-yellow-50' };
      case 'success':
        return { icon: <CheckCircle size={16} />, color: 'text-green-600', bg: 'bg-green-50' };
      case 'info':
        return { icon: <Info size={16} />, color: 'text-blue-600', bg: 'bg-blue-50' };
      case 'debug':
        return { icon: <Info size={16} />, color: 'text-gray-600', bg: 'bg-gray-50' };
      default:
        return { icon: <Info size={16} />, color: 'text-gray-600', bg: 'bg-white' };
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  // Toggle log expansion
  const toggleLogExpansion = (logId) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  // Copy log to clipboard
  const copyLogToClipboard = (log) => {
    const logText = `[${formatTimestamp(log.timestamp)}] ${log.level.toUpperCase()}: ${log.message}${
      log.data ? '\nData: ' + JSON.stringify(log.data, null, 2) : ''
    }`;

    navigator.clipboard.writeText(logText).then(() => {
      // Could show a toast notification here
    });
  };

  // Export logs
  const exportLogs = () => {
    const logsText = filteredLogs.map(log =>
      `[${formatTimestamp(log.timestamp)}] ${log.level.toUpperCase()}: ${log.message}${
        log.data ? '\nData: ' + JSON.stringify(log.data, null, 2) : ''
      }`
    ).join('\n\n');

    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `console-logs-${new Date().toISOString().slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`flex flex-col h-full bg-gray-900 text-green-400 font-mono ${compact ? 'text-xs' : 'text-sm'} ${className}`}>
      {/* Console Header */}
      <div className={`flex items-center justify-between ${compact ? 'p-2' : 'p-2'} bg-gray-800 border-b border-gray-700`}>
        <div className="flex items-center space-x-2">
          <Terminal size={compact ? 12 : 16} className="text-green-400" />
          <h3 className="text-white font-semibold">{compact ? 'Console' : 'Live Console'}</h3>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${isProcessing ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`} />
            <span className="text-xs text-gray-400">
              {isProcessing ? 'Live' : 'Idle'}
            </span>
          </div>
          {!compact && (
            <span className="text-xs text-gray-400">
              {filteredLogs.length} / {logs.length} logs
            </span>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {/* Auto-scroll toggle */}
          <button
            onClick={() => setIsAutoScrollEnabled(!isAutoScrollEnabled)}
            className={`px-2 py-1 text-xs rounded ${
              isAutoScrollEnabled
                ? 'bg-green-600 text-white'
                : 'bg-gray-600 text-gray-300'
            }`}
            title="Toggle auto-scroll"
          >
            {compact ? '↓' : 'Auto-scroll'}
          </button>

          {/* Export logs */}
          {onExport && (
            <button
              onClick={onExport}
              className="p-1 text-gray-400 hover:text-white"
              title="Export logs"
            >
              <Download size={compact ? 12 : 16} />
            </button>
          )}

          {/* Clear logs */}
          {onClear && (
            <button
              onClick={onClear}
              className="p-1 text-gray-400 hover:text-red-400"
              title="Clear logs"
            >
              <Trash2 size={compact ? 12 : 16} />
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      {!compact && (
        <div className="flex items-center space-x-1 px-2 py-1 bg-gray-800 border-b border-gray-700 text-xs">
          {/* Search */}
          <div className="flex-1 relative min-w-0">
            <Search size={12} className="absolute left-1.5 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-6 pr-2 py-0.5 bg-gray-700 text-white text-xs rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
            />
          </div>

          {/* Level filter */}
          <select
            value={levelFilter}
            onChange={(e) => setLevelFilter(e.target.value)}
            className="px-1.5 py-0.5 bg-gray-700 text-white text-xs rounded border border-gray-600 focus:border-blue-500 focus:outline-none"
          >
            <option value="all">All</option>
            <option value="error">Error</option>
            <option value="warning">Warn</option>
            <option value="success">Success</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>

          {/* Step filter */}
          <select
            value={stepFilter}
            onChange={(e) => setStepFilter(e.target.value)}
            className="px-1.5 py-0.5 bg-gray-700 text-white text-xs rounded border border-gray-600 focus:border-blue-500 focus:outline-none max-w-24"
          >
            <option value="all">All Steps</option>
            {uniqueSteps.map(step => (
              <option key={step} value={step}>{step.length > 10 ? step.substring(0, 10) + '...' : step}</option>
            ))}
          </select>
        </div>
      )}

      {/* Console Content */}
      <div
        ref={consoleRef}
        className="flex-1 overflow-y-auto px-2 py-1 space-y-1"
      >
        {filteredLogs.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {logs.length === 0 ? 'No logs yet...' : 'No logs match current filters'}
          </div>
        ) : (
          filteredLogs.map((log) => {
            const levelInfo = getLogLevelInfo(log.level);
            const isExpanded = expandedLogs.has(log.id);
            const hasData = log.data && Object.keys(log.data).length > 0;

            return (
              <div key={log.id} className="group">
                <div className="flex items-start space-x-2 hover:bg-gray-800 px-1 py-0.5 rounded">
                  {/* Timestamp */}
                  <span className="text-gray-500 text-xs flex-shrink-0 w-16">
                    {formatTimestamp(log.timestamp)}
                  </span>

                  {/* Level icon */}
                  <div className={`flex-shrink-0 ${levelInfo.color}`}>
                    {levelInfo.icon}
                  </div>

                  {/* Step badge */}
                  {log.step && (
                    <span className="bg-blue-600 text-white px-1 py-0.5 text-xs rounded flex-shrink-0">
                      {log.step}
                    </span>
                  )}

                  {/* Message */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      {hasData && (
                        <button
                          onClick={() => toggleLogExpansion(log.id)}
                          className="text-gray-500 hover:text-white"
                        >
                          {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
                        </button>
                      )}
                      <span className="break-words">{log.message}</span>
                    </div>

                    {/* Expanded data */}
                    {hasData && isExpanded && (
                      <div className="mt-1 ml-4 p-2 bg-gray-800 rounded text-xs">
                        <pre className="whitespace-pre-wrap text-yellow-300">
                          {JSON.stringify(log.data, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>

                  {/* Copy button */}
                  <button
                    onClick={() => copyLogToClipboard(log)}
                    className="opacity-0 group-hover:opacity-100 text-gray-500 hover:text-white transition-opacity"
                    title="Copy log"
                  >
                    <Copy size={12} />
                  </button>
                </div>
              </div>
            );
          })
        )}

        {/* Auto-scroll anchor */}
        <div ref={bottomRef} />
      </div>
    </div>
  );
};

export default LiveConsoleLogger;
