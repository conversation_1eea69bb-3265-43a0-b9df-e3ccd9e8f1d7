import React, { useState, useEffect, useRef } from 'react';

/**
 * Console Logger Component for Multi-Step Pipeline
 * Displays real-time processing logs with filtering and export capabilities
 */
const ConsoleLogger = ({
  logs = [],
  isProcessing = false,
  onClearLogs,
  onExportLogs,
  className = ''
}) => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoScroll, setAutoScroll] = useState(true);
  const logsEndRef = useRef(null);
  const containerRef = useRef(null);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, autoScroll]);

  // Handle manual scroll to disable auto-scroll
  const handleScroll = () => {
    if (containerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    }
  };

  // Filter logs based on level and search term
  const filteredLogs = logs.filter(log => {
    const matchesFilter = filter === 'all' || log.level === filter;
    const matchesSearch = !searchTerm ||
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.stepName?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Get log level counts
  const logCounts = logs.reduce((acc, log) => {
    acc[log.level] = (acc[log.level] || 0) + 1;
    acc.total = (acc.total || 0) + 1;
    return acc;
  }, {});

  // Format timestamp
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  // Get log level styling
  const getLogLevelStyle = (level) => {
    switch (level) {
      case 'error':
        return 'text-red-600 bg-red-50 border-l-red-500';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-l-yellow-500';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-l-blue-500';
      case 'success':
        return 'text-green-600 bg-green-50 border-l-green-500';
      case 'debug':
        return 'text-gray-600 bg-gray-50 border-l-gray-500';
      default:
        return 'text-gray-700 bg-white border-l-gray-300';
    }
  };

  // Get log level icon
  const getLogLevelIcon = (level) => {
    switch (level) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      case 'success': return '✅';
      case 'debug': return '🔍';
      default: return '📝';
    }
  };

  return (
    <div className={`flex flex-col h-full bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2">
          <h3 className="text-sm font-semibold text-gray-900">Console Logs</h3>
          {isProcessing && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-xs text-blue-600">Processing...</span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">
            {logCounts.total || 0} logs
          </span>
          <button
            onClick={onClearLogs}
            className="text-xs text-gray-500 hover:text-red-600 transition-colors"
            title="Clear logs"
          >
            🗑️
          </button>
          <button
            onClick={onExportLogs}
            className="text-xs text-gray-500 hover:text-blue-600 transition-colors"
            title="Export logs"
          >
            📤
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="p-3 border-b border-gray-200 space-y-2">
        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-1">
          {['all', 'error', 'warning', 'info', 'success', 'debug'].map(level => (
            <button
              key={level}
              onClick={() => setFilter(level)}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                filter === level
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {level === 'all' ? 'All' : level.charAt(0).toUpperCase() + level.slice(1)}
              {level !== 'all' && logCounts[level] && (
                <span className="ml-1 text-xs">({logCounts[level]})</span>
              )}
            </button>
          ))}
        </div>

        {/* Search Input */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search logs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}
        </div>

        {/* Auto-scroll Toggle */}
        <div className="flex items-center justify-between">
          <label className="flex items-center space-x-1 text-xs text-gray-600">
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
              className="w-3 h-3"
            />
            <span>Auto-scroll</span>
          </label>
          <span className="text-xs text-gray-500">
            Showing {filteredLogs.length} of {logs.length} logs
          </span>
        </div>
      </div>

      {/* Logs Container */}
      <div
        ref={containerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto p-2 space-y-1"
        style={{ maxHeight: '400px' }}
      >
        {filteredLogs.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500 text-sm">
            {logs.length === 0 ? 'No logs yet' : 'No logs match current filter'}
          </div>
        ) : (
          filteredLogs.map((log, index) => (
            <div
              key={`${log.timestamp}-${index}`}
              className={`p-2 border-l-4 rounded text-xs font-mono ${getLogLevelStyle(log.level)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-2 flex-1">
                  <span className="flex-shrink-0">{getLogLevelIcon(log.level)}</span>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-xs font-semibold text-gray-600">
                        {formatTime(log.timestamp)}
                      </span>
                      {log.stepName && (
                        <span className="px-1 py-0.5 bg-gray-200 text-gray-700 rounded text-xs">
                          {log.stepName}
                        </span>
                      )}
                    </div>
                    <div className="break-words">
                      {log.message}
                    </div>
                    {log.data && (
                      <details className="mt-1">
                        <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                          Show data
                        </summary>
                        <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                          {JSON.stringify(log.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={logsEndRef} />
      </div>
    </div>
  );
};

export default ConsoleLogger;
