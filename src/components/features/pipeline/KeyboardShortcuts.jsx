/**
 * Keyboard Shortcuts System for Pipeline Window
 * Provides keyboard navigation and quick actions
 */

import React, { useEffect, useCallback, useState } from 'react';
import { Command, Keyboard } from 'lucide-react';

// Keyboard shortcut definitions
export const SHORTCUTS = {
  // Pipeline controls
  RUN_PIPELINE: { key: 'r', ctrl: true, description: 'Run full pipeline' },
  PAUSE_PIPELINE: { key: 'p', ctrl: true, description: 'Pause pipeline' },
  RESTART_PIPELINE: { key: 'r', ctrl: true, shift: true, description: 'Restart pipeline' },

  // View controls
  TOGGLE_VIEW_MODE: { key: 'v', ctrl: true, description: 'Toggle view mode' },
  TOGGLE_CONSOLE: { key: 'c', ctrl: true, description: 'Toggle console logs' },
  FULLSCREEN: { key: 'f', ctrl: true, description: 'Toggle fullscreen' },

  // Navigation
  NEXT_STEP: { key: 'ArrowDown', description: 'Focus next step' },
  PREV_STEP: { key: 'ArrowUp', description: 'Focus previous step' },
  EXPAND_STEP: { key: 'Enter', description: 'Expand/collapse focused step' },

  // Actions
  COPY_LOGS: { key: 'c', ctrl: true, shift: true, description: 'Copy console logs' },
  EXPORT_LOGS: { key: 'e', ctrl: true, description: 'Export logs' },
  CLEAR_LOGS: { key: 'l', ctrl: true, shift: true, description: 'Clear logs' },

  // Window controls
  CLOSE_WINDOW: { key: 'w', ctrl: true, description: 'Close window' },
  MINIMIZE: { key: 'm', ctrl: true, description: 'Minimize window' },

  // Help
  SHOW_SHORTCUTS: { key: '?', shift: true, description: 'Show keyboard shortcuts' }
};

/**
 * Keyboard Shortcuts Hook
 */
export const useKeyboardShortcuts = (handlers = {}) => {
  const [activeShortcuts, setActiveShortcuts] = useState(new Set());

  const handleKeyDown = useCallback((event) => {
    const { key, ctrlKey, shiftKey, altKey, metaKey } = event;

    // Find matching shortcut
    const shortcut = Object.entries(SHORTCUTS).find(([, config]) => {
      return (
        config.key === key &&
        !!config.ctrl === (ctrlKey || metaKey) &&
        !!config.shift === shiftKey &&
        !!config.alt === altKey
      );
    });

    if (shortcut) {
      const [shortcutName, config] = shortcut;
      const handler = handlers[shortcutName];

      if (handler) {
        event.preventDefault();
        event.stopPropagation();

        // Visual feedback
        setActiveShortcuts(prev => new Set([...prev, shortcutName]));
        setTimeout(() => {
          setActiveShortcuts(prev => {
            const newSet = new Set(prev);
            newSet.delete(shortcutName);
            return newSet;
          });
        }, 200);

        handler(event);
      }
    }
  }, [handlers]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { activeShortcuts };
};

/**
 * Keyboard Shortcuts Help Modal
 */
export const KeyboardShortcutsHelp = ({ isOpen, onClose }) => {
  useEffect(() => {
    if (isOpen) {
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          onClose();
        }
      };
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  if (!isOpen) { return null; }

  const formatShortcut = (config) => {
    const parts = [];
    if (config.ctrl) { parts.push('Ctrl'); }
    if (config.shift) { parts.push('Shift'); }
    if (config.alt) { parts.push('Alt'); }

    // Format special keys
    let keyDisplay = config.key;
    if (config.key === 'ArrowUp') { keyDisplay = '↑'; } else if (config.key === 'ArrowDown') { keyDisplay = '↓'; } else if (config.key === 'ArrowLeft') { keyDisplay = '←'; } else if (config.key === 'ArrowRight') { keyDisplay = '→'; } else if (config.key === 'Enter') { keyDisplay = '⏎'; } else if (config.key === ' ') { keyDisplay = 'Space'; }

    parts.push(keyDisplay);
    return parts;
  };

  const shortcutCategories = {
    'Pipeline Controls': ['RUN_PIPELINE', 'PAUSE_PIPELINE', 'RESTART_PIPELINE'],
    'View Controls': ['TOGGLE_VIEW_MODE', 'TOGGLE_CONSOLE', 'FULLSCREEN'],
    'Navigation': ['NEXT_STEP', 'PREV_STEP', 'EXPAND_STEP'],
    'Actions': ['COPY_LOGS', 'EXPORT_LOGS', 'CLEAR_LOGS'],
    'Window': ['CLOSE_WINDOW', 'MINIMIZE'],
    'Help': ['SHOW_SHORTCUTS']
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Keyboard className="text-blue-600" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">Keyboard Shortcuts</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid gap-6">
            {Object.entries(shortcutCategories).map(([category, shortcutNames]) => (
              <div key={category}>
                <h3 className="text-lg font-medium text-gray-900 mb-3">{category}</h3>
                <div className="space-y-2">
                  {shortcutNames.map(shortcutName => {
                    const config = SHORTCUTS[shortcutName];
                    const keyParts = formatShortcut(config);

                    return (
                      <div key={shortcutName} className="flex items-center justify-between py-2">
                        <span className="text-gray-700">{config.description}</span>
                        <div className="flex items-center space-x-1">
                          {keyParts.map((part, index) => (
                            <React.Fragment key={index}>
                              {index > 0 && <span className="text-gray-400">+</span>}
                              <kbd className="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-sm font-mono">
                                {part}
                              </kbd>
                            </React.Fragment>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <p className="text-sm text-gray-600">
            Press <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Esc</kbd> to close this dialog
          </p>
        </div>
      </div>
    </div>
  );
};

/**
 * Keyboard Shortcut Indicator
 * Shows active shortcuts with visual feedback
 */
export const KeyboardShortcutIndicator = ({ activeShortcuts }) => {
  if (activeShortcuts.size === 0) { return null; }

  return (
    <div className="fixed bottom-4 left-4 z-40">
      <div className="bg-gray-900 text-white px-3 py-2 rounded-lg shadow-lg">
        <div className="flex items-center space-x-2">
          <Command size={16} />
          <span className="text-sm">
            {Array.from(activeShortcuts).map(shortcut =>
              SHORTCUTS[shortcut]?.description
            ).join(', ')}
          </span>
        </div>
      </div>
    </div>
  );
};

/**
 * Shortcut Hint Component
 * Shows available shortcuts for current context
 */
export const ShortcutHint = ({ shortcuts = [], className = '' }) => {
  if (shortcuts.length === 0) { return null; }

  return (
    <div className={`text-xs text-gray-500 ${className}`}>
      {shortcuts.map((shortcutName, index) => {
        const config = SHORTCUTS[shortcutName];
        if (!config) { return null; }

        const keyParts = [];
        if (config.ctrl) { keyParts.push('Ctrl'); }
        if (config.shift) { keyParts.push('Shift'); }
        keyParts.push(config.key);

        return (
          <span key={shortcutName}>
            {index > 0 && ' • '}
            <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">
              {keyParts.join('+')}
            </kbd>
            {' '}
            {config.description}
          </span>
        );
      })}
    </div>
  );
};

export default useKeyboardShortcuts;
